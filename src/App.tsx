import React, { useState, useEffect } from 'react';
import { LoginScreen } from './components/LoginScreen';
import { CommunityNoteEditor } from './components/CommunityNoteEditor';
import { CommunityNoteCard } from './components/CommunityNoteCard';
import { EmptyState } from './components/EmptyState';
import { ConnectionStatus } from './components/ConnectionStatus';
import { PWAInstallPrompt } from './components/PWAInstallPrompt';
import { HelloKittySticker } from './components/HelloKittySticker';
import { FloatingStickers } from './components/FloatingStickers';
import { useCommunityNotes } from './hooks/useCommunityNotes';
import { useAuth } from './hooks/useAuth';
import { usePWA } from './hooks/usePWA';
import { Plus, LogOut, Sparkles, Wifi, WifiOff, Search } from 'lucide-react';
import type { CommunityNote } from './lib/supabase';

function App() {
  const { user, isLoading: authLoading, login, logout, isAuthenticated } = useAuth();
  const { notes, loading, error, isOnline, lastSync, saveNote, deleteNote } = useCommunityNotes();
  const { isInstallable, installApp } = usePWA();
  
  const [selectedNote, setSelectedNote] = useState<CommunityNote | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [shortcutAction, setShortcutAction] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Handle PWA shortcuts
  useEffect(() => {
    const handlePWAShortcut = (event: CustomEvent) => {
      const { action } = event.detail;
      
      if (!isAuthenticated) return;
      
      setShortcutAction(action);
      
      // Auto-open editor for relevant actions
      if (['new', 'voice', 'photo'].includes(action)) {
        handleOpenEditor();
      }
    };

    window.addEventListener('pwa-shortcut', handlePWAShortcut as EventListener);
    
    return () => {
      window.removeEventListener('pwa-shortcut', handlePWAShortcut as EventListener);
    };
  }, [isAuthenticated]);

  const filteredNotes = notes.filter(note => {
    const term = searchTerm.toLowerCase();
    const titleMatch = note.title?.toLowerCase().includes(term);
    const contentMatch = note.content?.toLowerCase().includes(term);
    return titleMatch || contentMatch;
  });

  // Show loading screen during auth check
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-blue-50 to-purple-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 border-4 border-pink-300 border-t-pink-600 rounded-full animate-spin"></div>
          <span className="text-gray-600">Loading Stories...</span>
        </div>
      </div>
    );
  }

  // Show login screen if not authenticated
  if (!isAuthenticated) {
    return <LoginScreen onLogin={login} />;
  }

  const handleOpenEditor = (note?: CommunityNote) => {
    setSelectedNote(note || null);
    setIsEditorOpen(true);
  };

  const handleCloseEditor = () => {
    setSelectedNote(null);
    setIsEditorOpen(false);
  };

  const handleSaveNote = async (noteData: any, isAutoSave = false) => {
    try {
      // DUPLICATE PREVENTION: Additional check at app level
      if (!noteData.title?.trim() && !noteData.content?.trim()) {
        console.log('Empty note data, skipping save');
        return;
      }
      
      const savedNote = await saveNote({
        ...noteData,
        id: selectedNote?.id,
        author_name: user?.name || 'Anonymous',
        author_color: user?.color || '#6b7280'
      }, isAutoSave);
      
      if (!isAutoSave) {
        handleCloseEditor();
      }
      
      return savedNote;
    } catch (error: any) {
      // Handle duplicate prevention errors gracefully
      if (error.message?.includes('Duplicate note detected') || 
          error.code === '23505') { // PostgreSQL unique violation
        console.log('Duplicate note prevented at database level');
        if (!isAutoSave) {
          handleCloseEditor();
        }
                 return selectedNote || undefined; // Return existing note or undefined
      }
      
      console.error('Failed to save note:', error);
      throw error;
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    try {
      await deleteNote(noteId);
    } catch (error) {
      console.error('Failed to delete note:', error);
      throw error;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-100/60 via-white/40 to-blue-100/60 relative overflow-hidden">
      {/* Background Kawaii Decorations - Behind everything */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
        <FloatingStickers />
        
        {/* Background Decorative Elements */}
        <div className="absolute inset-0">
          {/* Top sparkles */}
          <div className="absolute top-12 left-1/4 text-3xl animate-sparkle text-yellow-400 opacity-20" style={{animationDelay: '0s'}}>
            ✨
          </div>
          <div className="absolute top-20 right-1/3 text-2xl animate-sparkle text-pink-400 opacity-15" style={{animationDelay: '2s'}}>
            🌟
          </div>
          <div className="absolute top-32 left-1/2 text-xl animate-sparkle text-blue-400 opacity-12" style={{animationDelay: '4s'}}>
            💫
          </div>
          
          {/* Side decorations */}
          <div className="absolute top-1/3 left-12 text-2xl animate-sparkle text-yellow-300 opacity-15" style={{animationDelay: '1s'}}>
            ⭐
          </div>
          <div className="absolute top-1/2 right-12 text-xl animate-sparkle text-pink-300 opacity-12" style={{animationDelay: '3s'}}>
            🌸
          </div>
          
          {/* Bottom sparkles */}
          <div className="absolute bottom-32 left-1/3 text-2xl animate-sparkle text-blue-300 opacity-15" style={{animationDelay: '5s'}}>
            💖
          </div>
          <div className="absolute bottom-20 right-1/4 text-xl animate-sparkle text-yellow-400 opacity-20" style={{animationDelay: '6s'}}>
            🎀
          </div>
          
          {/* Large floating Hello Kitty decorations - very subtle */}
          <div className="absolute top-24 right-24 opacity-5 animate-float" style={{animationDelay: '2s'}}>
            <HelloKittySticker size="large" pose="waving" />
          </div>
          <div className="absolute bottom-32 left-16 opacity-4 animate-gentle-bounce" style={{animationDelay: '4s'}}>
            <HelloKittySticker size="medium" pose="happy" />
          </div>
        </div>
      </div>

      {/* Floating Connection Status */}
      <div className="fixed top-4 right-4 z-50">
        <div className="flex items-center space-x-3">
          {/* Connection Indicator */}
          <div className={`flex items-center space-x-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-300 ${
            isOnline 
              ? 'bg-green-100 text-green-700 border border-green-200' 
              : 'bg-red-100 text-red-700 border border-red-200'
          }`}>
            {isOnline ? <Wifi size={12} /> : <WifiOff size={12} />}
            <span>{isOnline ? 'Online' : 'Offline'}</span>
          </div>

          {/* User Info & Logout */}
          <div className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-3 py-1.5 shadow-sm border border-gray-200">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold"
              style={{ backgroundColor: user?.color }}
            >
              {user?.name?.charAt(0)}
            </div>
            <span className="text-sm font-medium text-gray-700">{user?.name}</span>
            <button
              onClick={logout}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="Logout"
            >
              <LogOut size={14} />
            </button>
          </div>
        </div>
      </div>

      {/* PWA Install Prompt */}
      {isInstallable && <PWAInstallPrompt />}

      {/* Main Content Container */}
      <div className="container mx-auto px-4 py-8 max-w-7xl relative z-20">
        {/* Header Card */}
        <div className="bg-white/80 backdrop-blur-md rounded-3xl shadow-xl border-2 border-pink-200/50 p-6 sm:p-8 mb-8 relative overflow-hidden">
          {/* Header decorative elements */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-4 left-8 text-lg animate-sparkle text-pink-400 opacity-40" style={{animationDelay: '0s'}}>✨</div>
            <div className="absolute bottom-4 right-8 text-sm animate-sparkle text-blue-400 opacity-30" style={{animationDelay: '2s'}}>🌟</div>
            <div className="absolute top-4 right-1/3 text-xs animate-sparkle text-yellow-400 opacity-35" style={{animationDelay: '4s'}}>💫</div>
          </div>
          
          <div className="text-center relative z-10">
            <div className="flex items-center justify-center mb-4">
              <div className="relative">
                <Sparkles className="w-10 h-10 sm:w-12 sm:h-12 text-pink-500 animate-pulse mr-3" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-ping"></div>
              </div>
              <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 bg-clip-text text-transparent">
                🌸 Wicky and Batman Stories ✨
              </h1>
            </div>
            <p className="text-gray-600 max-w-3xl mx-auto text-base sm:text-lg leading-relaxed mb-6">
              Share your magical multimedia stories with beautiful photos, videos, and voice notes. Everything syncs in real-time between Batman and Wicky in this kawaii space! 🎀💖
            </p>
            
            {/* Search Input */}
            <div className="max-w-xl mx-auto mb-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search stories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-5 py-3 pr-12 rounded-full bg-white/70 border-2 border-pink-100/80 focus:ring-2 focus:ring-pink-300 focus:border-pink-300 transition-all duration-300 shadow-inner placeholder-gray-400 text-gray-700"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-4">
                  <Search className="w-5 h-5 text-gray-400" />
                </div>
              </div>
            </div>

            {/* Last Sync Info */}
            {lastSync && (
              <div className="inline-flex items-center space-x-2 bg-pink-50/80 px-4 py-2 rounded-full border border-pink-200">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-pink-600 font-medium">
                  Last synced: {lastSync.toLocaleTimeString()}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Floating Add Button */}
        <div className="fixed bottom-6 right-6 sm:bottom-8 sm:right-8 z-50">
          <button
            onClick={() => handleOpenEditor()}
            className="relative group w-16 h-16 sm:w-18 sm:h-18 bg-gradient-to-r from-pink-400 via-purple-400 to-blue-500 text-white rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 active:scale-95 transition-all duration-300 flex items-center justify-center overflow-hidden"
            style={{
              background: `linear-gradient(135deg, ${user?.color}22, ${user?.color})`
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            <Plus size={24} className="sm:w-7 sm:h-7 text-white relative z-10" />
            
            {/* Sparkles around button */}
            <div className="absolute -top-2 -right-2 text-sm animate-bounce text-yellow-400">
              ✨
            </div>
            <div className="absolute -bottom-2 -left-2 text-xs animate-pulse text-pink-300">
              🌟
            </div>
          </button>
          
          {/* Kawaii label */}
          <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-xs font-medium text-pink-600 border border-pink-200 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
            ✨ Create Magic Story
          </div>
        </div>

        {/* Notes Content */}
        <div className="relative">
          {loading && notes.length === 0 ? (
            <div className="text-center py-16">
              <div className="flex flex-col items-center space-y-4">
                <div className="relative">
                  <div className="w-8 h-8 border-4 border-pink-300 border-t-pink-600 rounded-full animate-spin"></div>
                  <div className="absolute -top-2 -right-2 text-lg animate-bounce text-yellow-400">
                    ✨
                  </div>
                </div>
                <span className="text-gray-700 font-medium text-lg">Loading your magical stories...</span>
                <div className="flex space-x-2 text-2xl opacity-60">
                  <span className="animate-bounce" style={{animationDelay: '0s'}}>🌸</span>
                  <span className="animate-bounce" style={{animationDelay: '0.2s'}}>💖</span>
                  <span className="animate-bounce" style={{animationDelay: '0.4s'}}>✨</span>
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="text-center py-16">
              <div className="bg-red-50/80 backdrop-blur-sm border-2 border-red-200 rounded-2xl p-8 max-w-md mx-auto relative">
                <div className="absolute -top-2 -right-2 text-lg animate-pulse text-red-400">
                  😿
                </div>
                <p className="text-red-600 font-bold mb-3 text-lg">Unable to load stories</p>
                <p className="text-red-500 text-sm mb-2">{error}</p>
                <p className="text-xs text-red-400">
                  {isOnline ? 'Check your connection' : 'You are offline'}
                </p>
              </div>
            </div>
          ) : notes.length > 0 && filteredNotes.length === 0 ? (
            <div className="py-8">
              <EmptyState onNewNote={() => handleOpenEditor()} isSearch={true} searchTerm={searchTerm} />
            </div>
          ) : filteredNotes.length === 0 ? (
            <div className="py-8">
              <EmptyState onNewNote={() => handleOpenEditor()} />
            </div>
          ) : (
            <>
              {/* Section title */}
              <div className="text-center mb-8">
                <h2 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-pink-500 to-blue-500 bg-clip-text text-transparent mb-2">
                  ✨ Your Magical Stories ✨
                </h2>
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-sm animate-bounce" style={{animationDelay: '0s'}}>🌸</span>
                  <div className="w-16 h-px bg-gradient-to-r from-pink-300 to-blue-300"></div>
                  <span className="text-sm animate-bounce" style={{animationDelay: '0.5s'}}>💖</span>
                  <div className="w-16 h-px bg-gradient-to-r from-blue-300 to-pink-300"></div>
                  <span className="text-sm animate-bounce" style={{animationDelay: '1s'}}>🎀</span>
                </div>
              </div>
              
              {/* Responsive Notes Grid - Fixed */}
              <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4">
                {filteredNotes.map((note, index) => (
                  <div 
                    key={note.id}
                    className="transform transition-all duration-300 hover:scale-[1.02] relative animate-fade-in-up"
                    style={{
                      animationDelay: `${index * 0.1}s`
                    }}
                  >
                    {/* Note card wrapper with extra kawaii styling */}
                    <div className="relative group h-full">
                      <CommunityNoteCard
                        note={note}
                        onEdit={() => handleOpenEditor(note)}
                        onDelete={() => handleDeleteNote(note.id)}
                      />
                      
                      {/* Floating decorations for each card */}
                      <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                        <div className="text-lg animate-bounce text-yellow-400">
                          ✨
                        </div>
                      </div>
                      <div className="absolute -bottom-2 -left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                        <div className="text-sm animate-pulse text-pink-400">
                          💖
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Note Editor Modal */}
      <CommunityNoteEditor
        note={selectedNote}
        onSave={handleSaveNote}
        onClose={handleCloseEditor}
        isOpen={isEditorOpen}
      />

      {/* Connection Status Component */}
      <ConnectionStatus isConnected={isOnline} onRefresh={() => {}} />
    </div>
  );
}

export default App;