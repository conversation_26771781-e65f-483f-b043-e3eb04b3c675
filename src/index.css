@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

@layer base {
  body {
    @apply antialiased;
    /* Prevent pull-to-refresh on iOS */
    overscroll-behavior-y: contain;
    /* Prevent zoom on iOS */
    touch-action: manipulation;
  }
  
  /* iOS PWA specific styles */
  @supports (-webkit-touch-callout: none) {
    .ios-safe-area {
      padding-top: env(safe-area-inset-top);
      padding-bottom: env(safe-area-inset-bottom);
      padding-left: env(safe-area-inset-left);
      padding-right: env(safe-area-inset-right);
    }
    
    /* Fix for iOS PWA status bar */
    .ios-status-bar-fix {
      padding-top: max(env(safe-area-inset-top), 20px);
    }
  }
  
  /* Prevent iOS input zoom */
  input, textarea, select {
    font-size: 16px !important;
    transform: translateZ(0);
    -webkit-appearance: none;
    border-radius: 0;
  }
  
  /* iOS PWA fixes */
  @media (display-mode: standalone) {
    body {
      -webkit-user-select: none;
      -webkit-touch-callout: none;
      -webkit-tap-highlight-color: transparent;
    }
    
    /* Allow text selection in content areas */
    .selectable-text {
      -webkit-user-select: text;
      user-select: text;
    }
  }
}

@layer utilities {
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .animate-in {
    animation: fadeInScale 0.3s ease-out forwards;
  }
  
  .fade-in {
    opacity: 0;
    transform: scale(0.95);
  }
  
  /* Modal animations */
  .zoom-in-95 {
    animation: zoomIn95 0.3s ease-out forwards;
  }
  
  /* iOS safe area utilities */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
  
  .safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomIn95 {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Hello Kitty Animations - Mobile Optimized */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-6px) rotate(1deg);
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-8px) rotate(-1deg);
  }
}

@keyframes float-medium {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-4px) rotate(0.5deg);
  }
}

@keyframes gentle-bounce {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-3px) scale(1.02);
  }
}

@keyframes gentle-sway {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  25% {
    transform: translateY(-6px) rotate(1deg) scale(1.01);
  }
  50% {
    transform: translateY(-8px) rotate(0deg) scale(1.02);
  }
  75% {
    transform: translateY(-6px) rotate(-1deg) scale(1.01);
  }
}

@keyframes bow-wiggle {
  0%, 100% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(2deg) scale(1.05);
  }
  50% {
    transform: rotate(0deg) scale(1.1);
  }
  75% {
    transform: rotate(-2deg) scale(1.05);
  }
}

@keyframes blink {
  0%, 85%, 100% {
    transform: scaleY(1);
  }
  90%, 95% {
    transform: scaleY(0.1);
  }
}

@keyframes whisker-twitch {
  0%, 100% {
    transform: translateX(0px) scaleX(1);
  }
  25% {
    transform: translateX(1px) scaleX(1.1);
  }
  50% {
    transform: translateX(0px) scaleX(1);
  }
  75% {
    transform: translateX(-0.5px) scaleX(1.05);
  }
}

@keyframes float-heart {
  0% {
    transform: translateY(0px) scale(1) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20px) scale(1.2) rotate(8deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-40px) scale(0.8) rotate(15deg);
    opacity: 0;
  }
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: scale(1.2) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(0.8) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    opacity: 0.9;
  }
}

@keyframes float-bow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  33% {
    transform: translateY(-8px) rotate(4deg) scale(1.08);
  }
  66% {
    transform: translateY(-4px) rotate(-2deg) scale(1.04);
  }
}

@keyframes gentle-wave {
  0%, 100% {
    transform: rotate(-12deg);
  }
  25% {
    transform: rotate(-20deg);
  }
  50% {
    transform: rotate(-35deg);
  }
  75% {
    transform: rotate(-20deg);
  }
}

@keyframes wave {
  0%, 100% {
    transform: rotate(-30deg);
  }
  50% {
    transform: rotate(-50deg);
  }
}

/* Smooth transition animations */
@keyframes smooth-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes smooth-slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 4s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 3s ease-in-out infinite;
}

.animate-gentle-bounce {
  animation: gentle-bounce 2.5s ease-in-out infinite;
}

.animate-gentle-sway {
  animation: gentle-sway 5s ease-in-out infinite;
}

.animate-bow-wiggle {
  animation: bow-wiggle 3s ease-in-out infinite;
}

.animate-blink {
  animation: blink 4s ease-in-out infinite;
}

.animate-whisker-twitch {
  animation: whisker-twitch 3.5s ease-in-out infinite;
}

.animate-float-heart {
  animation: float-heart 4s ease-out infinite;
}

.animate-sparkle {
  animation: sparkle 3s ease-in-out infinite;
}

.animate-float-bow {
  animation: float-bow 4s ease-in-out infinite;
}

.animate-gentle-wave {
  animation: gentle-wave 2.5s ease-in-out infinite;
}

.animate-wave {
  animation: wave 1.2s ease-in-out infinite;
}

.animate-smooth-fade-in {
  animation: smooth-fade-in 0.8s ease-out forwards;
}

.animate-smooth-slide-up {
  animation: smooth-slide-up 0.6s ease-out forwards;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
}

.animate-stagger-fade-in {
  animation: stagger-fade-in 0.5s ease-out forwards;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  /* Reduce animation intensity on mobile */
  .animate-float-heart {
    animation-duration: 5s;
  }
  
  .animate-sparkle {
    animation-duration: 4s;
  }
  
  .animate-bow-wiggle {
    animation-duration: 4s;
  }
  
  /* Reduce motion for better mobile performance */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-float-slow,
    .animate-float-medium,
    .animate-gentle-bounce,
    .animate-gentle-sway,
    .animate-bow-wiggle,
    .animate-blink,
    .animate-whisker-twitch,
    .animate-float-heart,
    .animate-sparkle,
    .animate-float-bow,
    .animate-gentle-wave,
    .animate-wave,
    .animate-smooth-fade-in,
    .animate-smooth-slide-up {
      animation: none;
    }
  }
}

/* Responsive text sizing */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  html {
    font-size: 15px;
  }
}

@media (min-width: 1025px) {
  html {
    font-size: 16px;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 228, 232, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 182, 193, 0.6);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 182, 193, 0.8);
}

/* Smooth focus rings */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-pink-200 focus:border-pink-300;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Smooth page transitions */
.page-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced mobile touch targets */
@media (max-width: 640px) {
  button, .clickable {
    min-height: 44px;
    min-width: 44px;
  }
}

/* iOS PWA specific fixes */
@media (display-mode: standalone) {
  /* Hide address bar space */
  body {
    padding-top: env(safe-area-inset-top);
  }
  
  /* Prevent text selection except in content areas */
  * {
    -webkit-user-select: none;
    user-select: none;
  }
  
  /* Allow text selection in specific areas */
  input, textarea, .selectable-text {
    -webkit-user-select: text;
    user-select: text;
  }
  
  /* Prevent tap highlight */
  * {
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Prevent callouts */
  * {
    -webkit-touch-callout: none;
  }
}

/* iOS specific input fixes */
@supports (-webkit-touch-callout: none) {
  input, textarea {
    /* Prevent zoom on focus */
    font-size: 16px !important;
    /* Fix appearance */
    -webkit-appearance: none;
    border-radius: 0;
    /* Improve performance */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }
}

/* Enhanced Cross-Platform PWA Optimizations */

/* Windows PWA optimizations */
@media (display-mode: window-controls-overlay) {
  .app-header {
    /* Account for Windows title bar overlay */
    padding-left: env(titlebar-area-x, 0);
    padding-right: calc(100vw - env(titlebar-area-x, 0) - env(titlebar-area-width, 100vw));
    padding-top: env(titlebar-area-height, 32px);
  }
}

/* macOS specific optimizations */
@media (display-mode: standalone) and (hover: hover) and (pointer: fine) {
  /* Better hover states for desktop with precise pointing */
  button:hover, .clickable:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
  }
  
  /* Smoother scrolling on macOS */
  * {
    scroll-behavior: smooth;
  }
}

/* Android Chrome specific optimizations */
@media (display-mode: standalone) and (pointer: coarse) {
  /* Better touch feedback */
  button:active, .clickable:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
  
  /* Improve touch precision */
  button, input, textarea, select, .clickable {
    min-height: 48px;
    min-width: 48px;
    padding: 12px;
  }
}

/* High refresh rate display optimization */
@media (min-resolution: 120dpi) {
  .animate-float,
  .animate-sparkle,
  .animate-gentle-bounce {
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Battery optimization for mobile devices */
@media (prefers-reduced-motion: no-preference) and (max-width: 768px) {
  /* Reduce animation complexity on mobile */
  .animate-float-heart {
    animation: simple-float 4s ease-in-out infinite;
  }
}

@keyframes simple-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-4px); }
}

/* Enhanced dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f0f0f;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --border-color: #333333;
  }
  
  .glass {
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  ::-webkit-scrollbar-track {
    background: rgba(42, 42, 42, 0.3);
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }
}

/* Print styles for PWA */
@media print {
  .no-print,
  .floating-add-button,
  .navigation-bar,
  .pwa-install-prompt {
    display: none !important;
  }
  
  .note-card {
    page-break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

/* Accessibility improvements */
@media (prefers-contrast: high) {
  button, input, textarea, select {
    border: 2px solid currentColor !important;
  }
  
  .glass {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.5);
  }
}

/* Focus visible improvements for keyboard navigation */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
.clickable:focus-visible {
  outline: 2px solid #ec4899;
  outline-offset: 2px;
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Memory optimization for large lists */
.virtualized-list {
  contain: layout style paint;
}

/* Network-aware styling */
@media (prefers-reduced-data: reduce) {
  /* Reduce visual effects for slow connections */
  .animate-sparkle,
  .animate-float-heart {
    animation: none;
  }
  
  /* Use simpler gradients */
  .bg-gradient-to-r {
    background: linear-gradient(90deg, #ec4899 0%, #f472b6 100%);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0px) scale(1);
  }
}

@keyframes stagger-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0px);
  }
}