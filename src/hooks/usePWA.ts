import { useState, useEffect } from 'react';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWAState {
  isInstallable: boolean;
  isInstalled: boolean;
  isOnline: boolean;
  platform: 'ios' | 'android' | 'windows' | 'mac' | 'unknown';
  installApp: () => Promise<boolean>;
  shareNote: (note: any) => Promise<boolean>;
}

export const usePWA = (): PWAState => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [platform, setPlatform] = useState<'ios' | 'android' | 'windows' | 'mac' | 'unknown'>('unknown');

  useEffect(() => {
    // Detect platform
    const detectPlatform = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const platform = navigator.platform?.toLowerCase() || '';
      
      if (/iphone|ipad|ipod/.test(userAgent)) {
        return 'ios';
      } else if (/android/.test(userAgent)) {
        return 'android';
      } else if (/win/.test(platform)) {
        return 'windows';
      } else if (/mac/.test(platform)) {
        return 'mac';
      }
      return 'unknown';
    };

    setPlatform(detectPlatform());

    // Check if app is already installed
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isInWebAppiOS = (window.navigator as any).standalone === true;
    const isInWebAppChrome = window.matchMedia('(display-mode: window-controls-overlay)').matches;
    
    setIsInstalled(isStandalone || isInWebAppiOS || isInWebAppChrome);

    // Listen for beforeinstallprompt event (Chrome/Edge)
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setIsInstallable(true);
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setDeferredPrompt(null);
    };

    // Listen for online/offline events
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    // Listen for PWA shortcuts
    const handlePWAShortcut = (event: CustomEvent) => {
      const { action } = event.detail;
      
      if (action === 'new') {
        // Trigger new note creation
        const newButton = document.querySelector('[data-action="new-note"]') as HTMLButtonElement;
        newButton?.click();
      } else if (action === 'search') {
        // Focus search input
        const searchInput = document.querySelector('input[placeholder*="Search"]') as HTMLInputElement;
        searchInput?.focus();
      }
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('pwa-shortcut', handlePWAShortcut as EventListener);

    // iOS specific detection
    const detectedPlatform = detectPlatform();
    if (detectedPlatform === 'ios') {
      // iOS doesn't fire beforeinstallprompt, so we show manual instructions
      const isIOSInstallable = !isInstalled && !window.matchMedia('(display-mode: standalone)').matches && !(window.navigator as any).standalone;
      setIsInstallable(isIOSInstallable);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('pwa-shortcut', handlePWAShortcut as EventListener);
    };
  }, []);

  const installApp = async (): Promise<boolean> => {
    // Handle iOS installation (manual instructions)
    if (platform === 'ios') {
      // iOS requires manual installation via Safari share menu
      return new Promise((resolve) => {
        resolve(true); // Return true to show instructions
      });
    }

    // Handle Android/Windows/Chrome installation
    if (!deferredPrompt) {
      // Fallback for browsers that don't support beforeinstallprompt
      if (platform === 'android') {
        alert(`To install on Android:
1. Open Chrome menu (three dots)
2. Tap "Add to Home screen" or "Install app"
3. Tap "Add" to confirm`);
      } else if (platform === 'windows') {
        alert(`To install on Windows:
1. Click the install icon in the address bar
2. Or open Edge menu and select "Apps" > "Install this site as an app"
3. Click "Install" to confirm`);
      }
      return false;
    }

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        setIsInstalled(true);
        setIsInstallable(false);
        setDeferredPrompt(null);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error installing app:', error);
      return false;
    }
  };

  const shareNote = async (note: any): Promise<boolean> => {
    if (!navigator.share) {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(`${note.title}\n\n${note.content}`);
        return true;
      } catch {
        return false;
      }
    }

    try {
      await navigator.share({
        title: note.title || 'Shared Note',
        text: note.content,
        url: window.location.href
      });
      return true;
    } catch (error) {
      console.error('Error sharing:', error);
      return false;
    }
  };

  return {
    isInstallable,
    isInstalled,
    isOnline,
    platform,
    installApp,
    shareNote
  };
};