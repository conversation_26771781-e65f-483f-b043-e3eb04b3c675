import { useState } from 'react';

interface ModalState {
  isOpen: boolean;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success' | 'confirm';
  confirmText: string;
  cancelText: string;
  showCancel: boolean;
  onConfirm?: () => void;
}

export const useModal = () => {
  const [modalState, setModalState] = useState<ModalState>({
    isOpen: false,
    title: '',
    message: '',
    type: 'info',
    confirmText: 'OK',
    cancelText: 'Cancel',
    showCancel: false,
    onConfirm: undefined
  });

  const showModal = (options: Partial<ModalState>) => {
    setModalState(prev => ({
      ...prev,
      isOpen: true,
      ...options
    }));
  };

  const hideModal = () => {
    setModalState(prev => ({
      ...prev,
      isOpen: false
    }));
  };

  const showAlert = (title: string, message: string, type: 'info' | 'warning' | 'error' | 'success' = 'info') => {
    showModal({
      title,
      message,
      type,
      showCancel: false,
      confirmText: 'OK'
    });
  };

  const showConfirm = (
    title: string, 
    message: string, 
    onConfirm: () => void,
    confirmText: string = 'Confirm',
    cancelText: string = 'Cancel'
  ) => {
    showModal({
      title,
      message,
      type: 'confirm',
      showCancel: true,
      confirmText,
      cancelText,
      onConfirm
    });
  };

  const showDeleteConfirm = (itemName: string, onConfirm: () => void) => {
    showModal({
      title: 'Delete Confirmation',
      message: `Are you sure you want to delete "${itemName}"? This action cannot be undone.`,
      type: 'warning',
      showCancel: true,
      confirmText: 'Delete',
      cancelText: 'Keep',
      onConfirm
    });
  };

  const showError = (message: string, title: string = 'Oops! Something went wrong') => {
    showModal({
      title,
      message,
      type: 'error',
      showCancel: false,
      confirmText: 'OK'
    });
  };

  const showSuccess = (message: string, title: string = 'Success! 🎉') => {
    showModal({
      title,
      message,
      type: 'success',
      showCancel: false,
      confirmText: 'Awesome!'
    });
  };

  const showInfo = (message: string, title: string = 'Just so you know 💡') => {
    showModal({
      title,
      message,
      type: 'info',
      showCancel: false,
      confirmText: 'Got it!'
    });
  };

  return {
    modalState,
    showModal,
    hideModal,
    showAlert,
    showConfirm,
    showDeleteConfirm,
    showError,
    showSuccess,
    showInfo
  };
};