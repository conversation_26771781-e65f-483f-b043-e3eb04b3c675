import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import type { CommunityNote } from '../lib/supabase';

interface CreateNoteData {
  title: string;
  content: string;
  author_name: string;
  author_color: string;
}

export const useCommunityNotes = () => {
  const [notes, setNotes] = useState<CommunityNote[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastSync, setLastSync] = useState<Date>(new Date());
  
  // Refs for managing subscriptions and intervals
  const channelRef = useRef<any>(null);
  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef<number>(0);
  const lastSyncRef = useRef<number>(Date.now());
  const isActiveRef = useRef<boolean>(true);
  
  // DUPLICATE PREVENTION: Track pending saves to prevent race conditions
  const pendingSavesRef = useRef<Map<string, Promise<CommunityNote | undefined>>>(new Map());
  const saveDebounceRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Enhanced fetch with retry logic and optimistic updates
  const fetchNotes = useCallback(async (retryCount = 0) => {
    try {
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('community_notes')
        .select('*')
        .order('created_at', { ascending: false });

      if (fetchError) {
        throw fetchError;
      }

      setNotes(data || []);
      setLastSync(new Date());
      lastSyncRef.current = Date.now();
      reconnectAttemptsRef.current = 0;
      
      if (loading) setLoading(false);
    } catch (err: any) {
      console.error('Error fetching notes:', err);
      
      // Exponential backoff retry logic
      if (retryCount < 3 && isOnline) {
        const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
        setTimeout(() => fetchNotes(retryCount + 1), delay);
      } else {
        setError(err.message || 'Failed to load notes');
        if (loading) setLoading(false);
      }
    }
  }, [loading, isOnline]);

  // Enhanced real-time subscription with better error handling
  const setupRealtimeSubscription = useCallback(() => {
    if (!isOnline || !isActiveRef.current) return;

    // Clean up existing subscription
    if (channelRef.current) {
      supabase.removeChannel(channelRef.current);
    }

    channelRef.current = supabase
      .channel('community_notes_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'community_notes'
        },
        (payload: any) => {
          console.log('🔄 Real-time update:', payload.eventType);
          lastSyncRef.current = Date.now();
          reconnectAttemptsRef.current = 0;
          
          setNotes(current => {
            if (payload.eventType === 'INSERT') {
              const newNote = payload.new as CommunityNote;
              // ENHANCED DUPLICATE PREVENTION: Check for duplicates by ID and content
              const isDuplicateById = current.some(note => note.id === newNote.id);
              const isDuplicateByContent = current.some(note => 
                note.title === newNote.title && 
                note.content === newNote.content &&
                note.author_name === newNote.author_name &&
                Math.abs(new Date(note.created_at).getTime() - new Date(newNote.created_at).getTime()) < 5000 // Within 5 seconds
              );
              
              if (isDuplicateById || isDuplicateByContent) {
                console.log('Duplicate note prevented in real-time update:', newNote.id);
                return current;
              }
              return [newNote, ...current];
            } 
            
            if (payload.eventType === 'UPDATE') {
              const updatedNote = payload.new as CommunityNote;
              return current.map(note => 
                note.id === updatedNote.id ? updatedNote : note
              );
            } 
            
            if (payload.eventType === 'DELETE') {
              const deletedId = payload.old.id;
              return current.filter(note => note.id !== deletedId);
            }
            
            return current;
          });
          
          setLastSync(new Date());
        }
      )
      .subscribe((status: string) => {
        console.log('📡 Subscription status:', status);
        
        if (status === 'SUBSCRIBED') {
          reconnectAttemptsRef.current = 0;
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          // Aggressive reconnection for seamless experience
          if (reconnectAttemptsRef.current < 10 && isActiveRef.current) {
            reconnectAttemptsRef.current++;
            const delay = Math.min(1000 * reconnectAttemptsRef.current, 5000);
            setTimeout(() => {
              if (isActiveRef.current) {
                setupRealtimeSubscription();
              }
            }, delay);
          }
        }
      });
  }, [isOnline]);

  // Aggressive sync interval for seamless updates
  const setupSyncInterval = useCallback(() => {
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current);
    }

    syncIntervalRef.current = setInterval(() => {
      if (!isActiveRef.current || !isOnline) return;
      
      const timeSinceLastSync = Date.now() - lastSyncRef.current;
      
      // More frequent syncing for better real-time feel
      if (timeSinceLastSync > 15000) { // 15 seconds
        console.log('⏰ Periodic sync triggered');
        fetchNotes();
      }
    }, 5000); // Check every 5 seconds
  }, [fetchNotes, isOnline]);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = () => {
      console.log('🌐 Back online - syncing notes');
      setIsOnline(true);
      fetchNotes();
    };

    const handleOffline = () => {
      console.log('📴 Gone offline');
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [fetchNotes]);

  // Enhanced visibility change handling
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        isActiveRef.current = false;
        console.log('👁️ App hidden - pausing updates');
      } else {
        isActiveRef.current = true;
        console.log('👁️ App visible - resuming updates');
        
        // Immediate sync when app becomes visible
        const timeSinceLastSync = Date.now() - lastSyncRef.current;
        if (timeSinceLastSync > 10000) { // 10 seconds
          fetchNotes();
        }
        
        // Re-establish real-time connection
        setupRealtimeSubscription();
      }
    };

    const handleFocus = () => {
      console.log('🎯 Window focused - checking for updates');
      if (isActiveRef.current) {
        const timeSinceLastSync = Date.now() - lastSyncRef.current;
        if (timeSinceLastSync > 5000) { // 5 seconds
          fetchNotes();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [fetchNotes, setupRealtimeSubscription]);

  // Initialize everything
  useEffect(() => {
    isActiveRef.current = true;
    fetchNotes();
    setupRealtimeSubscription();
    setupSyncInterval();

    return () => {
      isActiveRef.current = false;
      
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
      
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
      }
    };
  }, [fetchNotes, setupRealtimeSubscription, setupSyncInterval]);

  // Optimized save function with duplicate prevention
  const saveNote = async (noteData: Partial<CommunityNote>, isAutoSave = false): Promise<CommunityNote | undefined> => {
    try {
      setError(null);
      
      // DUPLICATE PREVENTION: Create a unique key for this save operation
      const saveKey = noteData.id || 'new-note';
      const contentHash = btoa(encodeURIComponent(`${noteData.title || ''}:${noteData.content || ''}`));
      const uniqueSaveKey = `${saveKey}:${contentHash}`;
      
      // Check if this exact save is already in progress
      if (pendingSavesRef.current.has(uniqueSaveKey)) {
        console.log('Duplicate save prevented for:', uniqueSaveKey);
        return pendingSavesRef.current.get(uniqueSaveKey);
      }
      
      // Clear any existing debounce timer for this note
      if (saveDebounceRef.current.has(saveKey)) {
        clearTimeout(saveDebounceRef.current.get(saveKey)!);
        saveDebounceRef.current.delete(saveKey);
      }
      
      // For auto-saves, add debouncing to prevent rapid consecutive saves
      if (isAutoSave) {
        return new Promise((resolve) => {
          const debounceTimer = setTimeout(async () => {
            saveDebounceRef.current.delete(saveKey);
            const result = await performSave(noteData, uniqueSaveKey);
            resolve(result);
          }, 1000); // 1 second debounce for auto-saves
          
          saveDebounceRef.current.set(saveKey, debounceTimer);
        });
      }
      
      // For manual saves, execute immediately
      return performSave(noteData, uniqueSaveKey);
      
    } catch (err: any) {
      console.error('Error saving note:', err);
      setError(err.message || 'Failed to save note');
      throw err;
    }
  };
  
  // Internal function to perform the actual save
  const performSave = async (noteData: Partial<CommunityNote>, uniqueSaveKey: string): Promise<CommunityNote | undefined> => {
    // Create the save promise
    const savePromise = (async () => {
      try {
        if (noteData.id) {
          // Update existing note - first check if content has actually changed
          const existingNote = notes.find(note => note.id === noteData.id);
          if (existingNote && 
              existingNote.title === noteData.title && 
              existingNote.content === noteData.content) {
            console.log('No changes detected, skipping save for note:', noteData.id);
            return existingNote;
          }
          
          const { data, error: updateError } = await supabase
            .from('community_notes')
            .update({
              title: noteData.title,
              content: noteData.content,
              author_name: noteData.author_name,
              author_color: noteData.author_color,
              updated_at: new Date().toISOString()
            })
            .eq('id', noteData.id)
            .select()
            .single();

          if (updateError) throw updateError;

          // Optimistic update
          setNotes(current => 
            current.map(note => note.id === data.id ? data : note)
          );
          
          lastSyncRef.current = Date.now();
          return data;
        } else {
          // Create new note - check for duplicate content in recent notes
          const recentNotes = notes.slice(0, 10); // Check last 10 notes
          const isDuplicate = recentNotes.some(note => 
            note.title === noteData.title && 
            note.content === noteData.content &&
            note.author_name === noteData.author_name &&
            (Date.now() - new Date(note.created_at).getTime()) < 60000 // Within last minute
          );
          
          if (isDuplicate) {
            console.log('Duplicate note content detected, skipping creation');
            const existingNote = recentNotes.find(note => 
              note.title === noteData.title && note.content === noteData.content
            );
            return existingNote;
          }
          
          const { data, error: insertError } = await supabase
            .from('community_notes')
            .insert({
              title: noteData.title || '',
              content: noteData.content || '',
              author_name: noteData.author_name || 'Anonymous',
              author_color: noteData.author_color || '#6b7280'
            })
            .select()
            .single();

          if (insertError) throw insertError;

          // Check if note was already added via real-time subscription
          const alreadyExists = notes.some(note => note.id === data.id);
          if (!alreadyExists) {
            setNotes(current => [data, ...current]);
          }
          
          lastSyncRef.current = Date.now();
          return data;
        }
      } finally {
        // Clean up the pending save
        pendingSavesRef.current.delete(uniqueSaveKey);
      }
    })();
    
    // Track this save promise
    pendingSavesRef.current.set(uniqueSaveKey, savePromise);
    
    return savePromise;
  };

  // Optimized delete function
  const deleteNote = async (noteId: string): Promise<void> => {
    try {
      setError(null);
      
      // Optimistic update
      setNotes(current => current.filter(note => note.id !== noteId));
      
      const { error: deleteError } = await supabase
        .from('community_notes')
        .delete()
        .eq('id', noteId);

      if (deleteError) {
        // Revert optimistic update on error
        fetchNotes();
        throw deleteError;
      }
      
      lastSyncRef.current = Date.now();
    } catch (err: any) {
      console.error('Error deleting note:', err);
      setError(err.message || 'Failed to delete note');
      throw err;
    }
  };

  return {
    notes,
    loading,
    error,
    isOnline,
    lastSync,
    saveNote,
    deleteNote,
    refetch: fetchNotes
  };
};