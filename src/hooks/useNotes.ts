import { useState, useEffect } from 'react';
import { notesDB } from '../utils/storage';

interface Note {
  id: string;
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
}

export const useNotes = () => {
  const [notes, setNotes] = useState<Note[]>([]);
  const [loading, setLoading] = useState(true);
  const [storageInfo, setStorageInfo] = useState({ used: 0, quota: 0 });

  // Load notes from IndexedDB on mount
  useEffect(() => {
    const loadNotes = async () => {
      try {
        await notesDB.init();
        const storedNotes = await notesDB.getAllNotes();
        setNotes(storedNotes);
        
        // Get storage info
        const info = await notesDB.getStorageInfo();
        setStorageInfo(info);
      } catch (error) {
        console.error('Error loading notes:', error);
        // Fallback to localStorage if IndexedDB fails
        try {
          const storedNotes = localStorage.getItem('kitty-notes');
          if (storedNotes) {
            const parsedNotes = JSON.parse(storedNotes).map((note: any) => ({
              ...note,
              createdAt: new Date(note.createdAt),
              updatedAt: new Date(note.updatedAt)
            }));
            setNotes(parsedNotes);
          }
        } catch (fallbackError) {
          console.error('Error loading from localStorage:', fallbackError);
        }
      } finally {
        setLoading(false);
      }
    };

    loadNotes();
  }, []);

  const addNote = async (noteData: Omit<Note, 'id'>) => {
    // DUPLICATE PREVENTION: Check for duplicate content in recent notes
    const recentNotes = notes.slice(0, 10); // Check last 10 notes
    const isDuplicate = recentNotes.some(note => 
      note.title === noteData.title && 
      note.content === noteData.content &&
      (Date.now() - note.createdAt.getTime()) < 60000 // Within last minute
    );
    
    if (isDuplicate) {
      console.log('Duplicate note content detected, returning existing note');
      const existingNote = recentNotes.find(note => 
        note.title === noteData.title && note.content === noteData.content
      );
      return existingNote!;
    }

    const newNote: Note = {
      ...noteData,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9)
    };

    try {
      await notesDB.saveNote(newNote);
      
      // DUPLICATE PREVENTION: Check if note was already added somehow
      const alreadyExists = notes.some(note => note.id === newNote.id);
      if (!alreadyExists) {
        setNotes(prev => [newNote, ...prev]);
      }
      
      // Update storage info
      const info = await notesDB.getStorageInfo();
      setStorageInfo(info);
    } catch (error) {
      console.error('Error saving note:', error);
      // Fallback to localStorage with duplicate check
      setNotes(prev => {
        const alreadyExists = prev.some(note => note.id === newNote.id);
        if (alreadyExists) {
          return prev;
        }
        const updated = [newNote, ...prev];
        localStorage.setItem('kitty-notes', JSON.stringify(updated));
        return updated;
      });
    }

    return newNote;
  };

  const updateNote = async (id: string, noteData: Omit<Note, 'id'>) => {
    const updatedNote = { ...noteData, id };

    try {
      await notesDB.saveNote(updatedNote);
      setNotes(prev => prev.map(note => 
        note.id === id ? updatedNote : note
      ));
      
      // Update storage info
      const info = await notesDB.getStorageInfo();
      setStorageInfo(info);
    } catch (error) {
      console.error('Error updating note:', error);
      // Fallback to localStorage
      setNotes(prev => {
        const updated = prev.map(note => 
          note.id === id ? updatedNote : note
        );
        localStorage.setItem('kitty-notes', JSON.stringify(updated));
        return updated;
      });
    }
  };

  const deleteNote = async (id: string) => {
    try {
      await notesDB.deleteNote(id);
      setNotes(prev => prev.filter(note => note.id !== id));
      
      // Update storage info
      const info = await notesDB.getStorageInfo();
      setStorageInfo(info);
    } catch (error) {
      console.error('Error deleting note:', error);
      // Fallback to localStorage
      setNotes(prev => {
        const updated = prev.filter(note => note.id !== id);
        localStorage.setItem('kitty-notes', JSON.stringify(updated));
        return updated;
      });
    }
  };

  const getNote = (id: string) => {
    return notes.find(note => note.id === id);
  };

  const clearAllNotes = async () => {
    try {
      await notesDB.clearAllNotes();
      setNotes([]);
      localStorage.removeItem('kitty-notes');
      
      // Update storage info
      const info = await notesDB.getStorageInfo();
      setStorageInfo(info);
    } catch (error) {
      console.error('Error clearing notes:', error);
    }
  };

  const formatStorageSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return {
    notes,
    loading,
    addNote,
    updateNote,
    deleteNote,
    getNote,
    clearAllNotes,
    storageInfo: {
      ...storageInfo,
      usedFormatted: formatStorageSize(storageInfo.used),
      quotaFormatted: formatStorageSize(storageInfo.quota),
      percentUsed: storageInfo.quota > 0 ? (storageInfo.used / storageInfo.quota) * 100 : 0
    }
  };
};