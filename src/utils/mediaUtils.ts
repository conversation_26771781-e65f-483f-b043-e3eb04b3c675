export class MediaUtils {
  static readonly MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
  static readonly MAX_VOICE_SIZE = 50 * 1024 * 1024; // 50MB
  static readonly MAX_VIDEO_SIZE = 100 * 1024 * 1024; // 100MB
  static readonly MAX_VOICE_DURATION = 600; // 10 minutes
  static readonly MAX_VIDEO_DURATION = 600; // 10 minutes
  
  static readonly ALLOWED_IMAGE_TYPES = [
    'image/jpeg',
    'image/png', 
    'image/gif',
    'image/webp'
  ];
  
  static readonly ALLOWED_VOICE_TYPES = [
    'audio/mp3',
    'audio/wav',
    'audio/m4a',
    'audio/webm',
    'audio/ogg'
  ];

  static readonly ALLOWED_VIDEO_TYPES = [
    'video/mp4',
    'video/webm',
    'video/quicktime',
    'video/x-msvideo'
  ];

  static validateImageFile(file: File): { valid: boolean; error?: string } {
    if (!this.ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return { valid: false, error: 'Unsupported image format. Please use JPEG, PNG, GIF, or WebP.' };
    }
    
    if (file.size > this.MAX_IMAGE_SIZE) {
      return { valid: false, error: 'Image too large. Maximum size is 10MB.' };
    }
    
    return { valid: true };
  }

  static validateVoiceFile(file: File): { valid: boolean; error?: string } {
    if (!this.ALLOWED_VOICE_TYPES.includes(file.type)) {
      return { valid: false, error: 'Unsupported audio format. Please use MP3, WAV, M4A, WebM, or OGG.' };
    }
    
    if (file.size > this.MAX_VOICE_SIZE) {
      return { valid: false, error: 'Audio file too large. Maximum size is 50MB.' };
    }
    
    return { valid: true };
  }

  static validateVideoFile(file: File): { valid: boolean; error?: string } {
    if (!this.ALLOWED_VIDEO_TYPES.includes(file.type)) {
      return { valid: false, error: 'Unsupported video format. Please use MP4, WebM, MOV, or AVI.' };
    }
    
    if (file.size > this.MAX_VIDEO_SIZE) {
      return { valid: false, error: 'Video file too large. Maximum size is 100MB.' };
    }
    
    return { valid: true };
  }

  static async compressImage(file: File, maxWidth: number = 1920, quality: number = 0.8): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
        
        canvas.width = width;
        canvas.height = height;
        
        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to compress image'));
            }
          },
          'image/jpeg',
          quality
        );
      };
      
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  static async generateThumbnail(file: File, size: number = 200): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        // Create square thumbnail
        const minDim = Math.min(img.width, img.height);
        const scale = size / minDim;
        
        canvas.width = size;
        canvas.height = size;
        
        // Center crop
        const sx = (img.width - minDim) / 2;
        const sy = (img.height - minDim) / 2;
        
        ctx?.drawImage(img, sx, sy, minDim, minDim, 0, 0, size, size);
        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to generate thumbnail'));
            }
          },
          'image/jpeg',
          0.7
        );
      };
      
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  static formatDuration(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  static async generateWaveform(audioBuffer: AudioBuffer, samples: number = 100): Promise<number[]> {
    const channelData = audioBuffer.getChannelData(0);
    const blockSize = Math.floor(channelData.length / samples);
    const waveform: number[] = [];
    
    for (let i = 0; i < samples; i++) {
      const start = i * blockSize;
      const end = start + blockSize;
      let sum = 0;
      
      for (let j = start; j < end; j++) {
        sum += Math.abs(channelData[j]);
      }
      
      waveform.push(sum / blockSize);
    }
    
    // Normalize to 0-1 range
    const max = Math.max(...waveform);
    return waveform.map(val => val / max);
  }

  static getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  static async getAudioDuration(file: File): Promise<number> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      audio.onloadedmetadata = () => {
        resolve(audio.duration);
      };
      audio.onerror = () => reject(new Error('Failed to load audio'));
      audio.src = URL.createObjectURL(file);
    });
  }

  static async getVideoInfo(file: File): Promise<{ width: number; height: number; duration: number }> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.onloadedmetadata = () => {
        resolve({
          width: video.videoWidth,
          height: video.videoHeight,
          duration: video.duration
        });
      };
      video.onerror = () => reject(new Error('Failed to load video'));
      video.src = URL.createObjectURL(file);
    });
  }

  static async generateVideoThumbnail(file: File, size: number = 200): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      video.onloadeddata = () => {
        // Seek to 10% of the video duration for a good thumbnail
        video.currentTime = video.duration * 0.1;
      };
      
      video.onseeked = () => {
        // Create square thumbnail
        const minDim = Math.min(video.videoWidth, video.videoHeight);
        const scale = size / minDim;
        
        canvas.width = size;
        canvas.height = size;
        
        // Center crop
        const sx = (video.videoWidth - minDim) / 2;
        const sy = (video.videoHeight - minDim) / 2;
        
        ctx?.drawImage(video, sx, sy, minDim, minDim, 0, 0, size, size);
        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to generate video thumbnail'));
            }
          },
          'image/jpeg',
          0.7
        );
      };
      
      video.onerror = () => reject(new Error('Failed to load video'));
      video.src = URL.createObjectURL(file);
    });
  }
}