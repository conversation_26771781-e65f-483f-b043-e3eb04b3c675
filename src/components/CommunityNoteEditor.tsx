import React, { useState, useEffect, useRef } from 'react';
import { Save, X, Sparkles } from 'lucide-react';
import { InlineMediaEditor } from './InlineMediaEditor';
import { HelloKittySticker } from './HelloKittySticker';
import { useModal } from '../hooks/useModal';
import { Modal } from './Modal';
import type { CommunityNote } from '../lib/supabase';
import type { InlineMediaElement } from '../types/media';

interface CommunityNoteEditorProps {
  note: CommunityNote | null;
  onSave: (noteData: any, isAutoSave?: boolean) => Promise<CommunityNote | undefined>;
  onClose: () => void;
  isOpen: boolean;
}

export const CommunityNoteEditor: React.FC<CommunityNoteEditorProps> = ({ 
  note, 
  onSave, 
  onClose, 
  isOpen 
}) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [mediaElements, setMediaElements] = useState<InlineMediaElement[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [savedNoteId, setSavedNoteId] = useState<string | null>(null);
  const [hasAutoSaved, setHasAutoSaved] = useState(false);
  
  // DUPLICATE PREVENTION: Track last save to prevent rapid consecutive saves
  const lastSaveRef = useRef<{ content: string; timestamp: number } | null>(null);
  const saveInProgressRef = useRef<boolean>(false);

  // Use modal hook
  const { modalState, hideModal, showInfo, showError } = useModal();

  useEffect(() => {
    if (note) {
      setTitle(note.title);
      setContent(note.content);
      setSavedNoteId(note.id);
      setHasAutoSaved(true);
    } else {
      setTitle('');
      setContent('');
      setMediaElements([]);
      setSavedNoteId(null);
      setHasAutoSaved(false);
    }
  }, [note]);

  const handleContentChange = (newContent: string, newMediaElements: InlineMediaElement[]) => {
    setContent(newContent);
    setMediaElements(newMediaElements);
  };

  // Auto-save functionality with duplicate prevention
  useEffect(() => {
    if (!hasAutoSaved && (title.trim() || content.trim())) {
      const autoSaveTimer = setTimeout(async () => {
        // DUPLICATE PREVENTION: Check if auto-save should proceed
        if (saveInProgressRef.current) {
          console.log('Manual save in progress, skipping auto-save');
          return;
        }
        
        const currentContent = `${title.trim()}:${content.trim()}`;
        const now = Date.now();
        
        if (lastSaveRef.current && 
            lastSaveRef.current.content === currentContent &&
            (now - lastSaveRef.current.timestamp) < 5000) { // Prevent auto-saves within 5 seconds of last save
          console.log('Auto-save prevented - content recently saved');
          return;
        }

        try {
          saveInProgressRef.current = true;
          setIsSaving(true);
          
          const noteData = {
            title: title.trim() || 'Untitled Note',
            content: content.trim()
          };
          
          const result = await onSave(noteData, true);
          
          if (result?.id) {
            setSavedNoteId(result.id);
            setHasAutoSaved(true);
            
            // Update last save tracking
            lastSaveRef.current = {
              content: currentContent,
              timestamp: now
            };
          }
        } catch (error) {
          console.error('Auto-save failed:', error);
        } finally {
          setIsSaving(false);
          saveInProgressRef.current = false;
        }
      }, 3000);

      return () => clearTimeout(autoSaveTimer);
    }
  }, [title, content, hasAutoSaved, onSave]);

  const handleSave = async () => {
    if (!title.trim() && !content.trim()) {
      showError("Please add some content before saving.");
      return;
    }

    // DUPLICATE PREVENTION: Check if save is already in progress or if content hasn't changed
    if (saveInProgressRef.current) {
      console.log('Save already in progress, skipping duplicate save');
      return;
    }

    const currentContent = `${title.trim()}:${content.trim()}`;
    const now = Date.now();
    
    if (lastSaveRef.current && 
        lastSaveRef.current.content === currentContent &&
        (now - lastSaveRef.current.timestamp) < 2000) { // Prevent saves within 2 seconds
      console.log('Duplicate save prevented - same content saved recently');
      return;
    }

    try {
      saveInProgressRef.current = true;
      setIsSaving(true);
      
      const noteData = {
        title: title.trim() || 'Untitled Note',
        content: content.trim()
      };
      
      await onSave(noteData, false);
      
      // Update last save tracking
      lastSaveRef.current = {
        content: currentContent,
        timestamp: now
      };
      
      onClose();
    } catch (error: any) {
      showError(`Failed to save note: ${error.message}`);
    } finally {
      setIsSaving(false);
      saveInProgressRef.current = false;
    }
  };

  const handleClose = () => {
    if (title.trim() || content.trim()) {
      if (!hasAutoSaved) {
        // Auto-save before closing
        handleSave().then(() => {
          if (!modalState.isOpen) { // Only close if no error modal is shown
            onClose();
          }
        });
        return;
      }
    }
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClose();
    } else if (e.key === 's' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    }
  };

  if (!isOpen) return null;

  const currentNoteId = note?.id || savedNoteId;

  return (
    <>
      <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center p-4 z-[70]">
        <div className="bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden border-2 border-pink-200/60 relative shadow-pink-100/50">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-pink-200/50 bg-gradient-to-r from-pink-50/80 to-purple-50/80">
            <div className="flex items-center space-x-3">
              <HelloKittySticker />
              <div>
                <h2 className="text-xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                  {note ? 'Edit Community Note' : 'New Community Note'}
                </h2>
                <p className="text-sm text-pink-500/80">
                  {hasAutoSaved ? '✨ Auto-saved' : 'Start writing to auto-save...'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {isSaving && (
                <div className="flex items-center space-x-2 text-pink-600">
                  <div className="w-4 h-4 border-2 border-pink-300 border-t-pink-600 rounded-full animate-spin"></div>
                  <span className="text-sm">Saving...</span>
                </div>
              )}
              
              <button
                onClick={handleSave}
                disabled={isSaving || (!title.trim() && !content.trim())}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-xl hover:from-pink-600 hover:to-purple-600 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className="w-4 h-4" />
                <span>Save</span>
              </button>
              
              <button
                onClick={handleClose}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-all duration-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[calc(90vh-140px)] overflow-y-auto" onKeyDown={handleKeyDown}>
            {/* Title Input */}
            <input
              type="text"
              placeholder="🌸 Give your note a magical title..."
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full text-2xl font-bold text-gray-800 bg-transparent border-none outline-none placeholder-pink-400/70 mb-6"
              style={{ fontSize: '24px' }}
            />

            <div className="w-full h-px bg-gradient-to-r from-pink-200 via-purple-300 to-pink-200 opacity-60 mb-6"></div>

            {/* Inline Media Editor */}
            <InlineMediaEditor
              noteId={currentNoteId}
              content={content}
              onContentChange={handleContentChange}
              placeholder="🌸 Start writing your magical story... ✨ Drop photos, videos, or audio files anywhere!"
              className="min-h-[400px]"
              disabled={isSaving}
            />
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-4 right-20 w-6 h-6 bg-gradient-to-br from-pink-300 to-pink-400 rounded-full opacity-30 animate-pulse"></div>
          <div className="absolute top-8 right-28 w-3 h-3 bg-gradient-to-br from-purple-300 to-purple-400 rounded-full opacity-30 animate-pulse delay-300"></div>
          <div className="absolute bottom-4 left-20 w-4 h-4 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full opacity-30 animate-pulse delay-700"></div>
        </div>
      </div>

      {/* Modal for notifications */}
      <Modal
        isOpen={modalState.isOpen}
        onClose={hideModal}
        onConfirm={modalState.onConfirm}
        title={modalState.title}
        message={modalState.message}
        type={modalState.type}
        confirmText={modalState.confirmText}
        cancelText={modalState.cancelText}
        showCancel={modalState.showCancel}
      />
    </>
  );
};