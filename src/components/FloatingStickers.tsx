import React, { useState, useEffect } from 'react';
import { HelloKittySticker } from './HelloKittySticker';

interface FloatingKitty {
  id: string;
  x: number;
  y: number;
  size: 'small' | 'medium';
  pose: 'classic' | 'winking' | 'happy' | 'sleepy' | 'waving';
  animationDelay: number;
  floatDirection: 'up' | 'down' | 'left' | 'right' | 'diagonal-up' | 'diagonal-down';
  speed: number;
  opacity: number;
  rotationSpeed: number;
  currentRotation: number;
}

export const FloatingStickers: React.FC = () => {
  const [floatingKitties, setFloatingKitties] = useState<FloatingKitty[]>([]);

  const poses: Array<'classic' | 'winking' | 'happy' | 'sleepy' | 'waving'> = ['classic', 'winking', 'happy', 'sleepy', 'waving'];
  const sizes: Array<'small' | 'medium'> = ['small', 'medium'];
  const directions: Array<'up' | 'down' | 'left' | 'right' | 'diagonal-up' | 'diagonal-down'> = ['up', 'down', 'left', 'right', 'diagonal-up', 'diagonal-down'];

  const createRandomKitty = (): FloatingKitty => {
    const isEdgeSpawn = Math.random() < 0.8; // 80% chance to spawn from edges
    let x, y;

    if (isEdgeSpawn) {
      // Spawn from edges with more variety
      const edge = Math.floor(Math.random() * 4);
      switch (edge) {
        case 0: // top
          x = Math.random() * window.innerWidth;
          y = -80 - Math.random() * 50; // More varied spawn distance
          break;
        case 1: // right
          x = window.innerWidth + 80 + Math.random() * 50;
          y = Math.random() * window.innerHeight;
          break;
        case 2: // bottom
          x = Math.random() * window.innerWidth;
          y = window.innerHeight + 80 + Math.random() * 50;
          break;
        default: // left
          x = -80 - Math.random() * 50;
          y = Math.random() * window.innerHeight;
          break;
      }
    } else {
      // Spawn randomly on screen
      x = Math.random() * (window.innerWidth - 200) + 100;
      y = Math.random() * (window.innerHeight - 200) + 100;
    }

    const maxLife = 4000 + Math.random() * 3000; // 4-7 seconds

    return {
      id: Math.random().toString(36).substr(2, 9),
      x,
      y,
      size: sizes[Math.floor(Math.random() * sizes.length)],
      pose: poses[Math.floor(Math.random() * poses.length)],
      animationDelay: Math.random() * 2,
      floatDirection: directions[Math.floor(Math.random() * directions.length)],
      speed: 0.4 + Math.random() * 0.8, // 0.4 to 1.2
      opacity: 0.1 + Math.random() * 0.2, // 0.1 to 0.3
      rotationSpeed: (Math.random() - 0.5) * 0.5, // -0.25 to 0.25 degrees per frame
      currentRotation: Math.random() * 360
    };
  };

  useEffect(() => {
    // Initial kitties - start with 7
    const initialKitties = Array.from({ length: 7 }, createRandomKitty);
    setFloatingKitties(initialKitties);

    // Spawn new kitties more frequently
    const spawnInterval = setInterval(() => {
      setFloatingKitties(prev => {
        // Remove kitties that are too far off screen
        const filtered = prev.filter(kitty => 
          kitty.x > -250 && kitty.x < window.innerWidth + 250 &&
          kitty.y > -250 && kitty.y < window.innerHeight + 250
        );

        // Add new kitty if we have less than 10 and random chance
        if (filtered.length < 10 && Math.random() < 0.7) {
          return [...filtered, createRandomKitty()];
        }
        return filtered;
      });
    }, 2000 + Math.random() * 1500); // 2-3.5 seconds

    // Animation loop for movement with more varied behavior
    const animationInterval = setInterval(() => {
      setFloatingKitties(prev => prev.map(kitty => {
        let newX = kitty.x;
        let newY = kitty.y;
        let newRotation = kitty.currentRotation + kitty.rotationSpeed;

        // Move based on direction with more variety
        switch (kitty.floatDirection) {
          case 'up':
            newY -= kitty.speed;
            newX += (Math.random() - 0.5) * 0.8; // More horizontal drift
            break;
          case 'down':
            newY += kitty.speed;
            newX += (Math.random() - 0.5) * 0.8;
            break;
          case 'left':
            newX -= kitty.speed;
            newY += (Math.random() - 0.5) * 0.8; // More vertical drift
            break;
          case 'right':
            newX += kitty.speed;
            newY += (Math.random() - 0.5) * 0.8;
            break;
          case 'diagonal-up':
            newX += kitty.speed * 0.7;
            newY -= kitty.speed * 0.7;
            newX += (Math.random() - 0.5) * 0.5;
            newY += (Math.random() - 0.5) * 0.5;
            break;
          case 'diagonal-down':
            newX -= kitty.speed * 0.7;
            newY += kitty.speed * 0.7;
            newX += (Math.random() - 0.5) * 0.5;
            newY += (Math.random() - 0.5) * 0.5;
            break;
        }

        // More frequent direction changes for more randomness
        let newDirection = kitty.floatDirection;
        if (Math.random() < 0.03) { // 3% chance per frame
          newDirection = directions[Math.floor(Math.random() * directions.length)];
        }

        // Occasionally change speed for more variety
        let newSpeed = kitty.speed;
        if (Math.random() < 0.01) { // 1% chance per frame
          newSpeed = 0.4 + Math.random() * 0.8;
        }

        return {
          ...kitty,
          x: newX,
          y: newY,
          floatDirection: newDirection,
          speed: newSpeed,
          currentRotation: newRotation
        };
      }));
    }, 40); // ~25fps for smooth movement

    return () => {
      clearInterval(spawnInterval);
      clearInterval(animationInterval);
    };
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none z-[1] overflow-hidden">
      {/* Random floating Hello Kitty stickers */}
      {floatingKitties.map(kitty => (
        <div
          key={kitty.id}
          className="absolute transition-all duration-100 ease-linear"
          style={{
            left: kitty.x,
            top: kitty.y,
            opacity: kitty.opacity,
            animationDelay: `${kitty.animationDelay}s`,
            transform: `scale(${kitty.size === 'small' ? 0.7 : 0.9}) rotate(${kitty.currentRotation + Math.sin(Date.now() * 0.001 + kitty.x * 0.01) * 8}deg)`
          }}
        >
          <HelloKittySticker 
            size={kitty.size} 
            pose={kitty.pose}
            animated={true}
          />
        </div>
      ))}

      {/* Static decorative elements - Background layer */}
      <div className="absolute top-24 left-1/2 sm:top-40 sm:left-1/3 text-2xl sm:text-3xl animate-sparkle text-yellow-400 opacity-40" style={{animationDelay: '0.5s'}}>
        ✨
      </div>
      
      <div className="absolute bottom-24 right-1/3 sm:bottom-40 sm:right-1/4 text-xl sm:text-2xl animate-sparkle text-pink-400 opacity-40" style={{animationDelay: '2s'}}>
        ⭐
      </div>
      
      <div className="absolute top-1/2 right-4 sm:left-24 text-lg sm:text-xl animate-sparkle text-blue-400 opacity-40" style={{animationDelay: '1s'}}>
        💫
      </div>
      
      <div className="absolute bottom-16 left-1/2 sm:bottom-28 text-xl sm:text-2xl animate-sparkle text-yellow-300 opacity-40" style={{animationDelay: '2.5s'}}>
        ✨
      </div>
      
      {/* Mobile-optimized PINK bows - Background layer */}
      <div className="absolute top-16 right-1/4 sm:top-32 sm:right-1/3 animate-float-bow opacity-50" style={{animationDelay: '1s'}}>
        <div className="w-8 h-8 sm:w-12 sm:h-12 relative">
          <div className="w-3 h-4 sm:w-5 sm:h-6 bg-pink-500 rounded-sm border border-black mx-auto shadow-lg relative">
            <div className="absolute inset-0 bg-gradient-to-b from-pink-400 to-pink-600 rounded-sm"></div>
          </div>
          <div className="absolute -left-2 sm:-left-4 top-0 w-4 h-4 sm:w-6 sm:h-6 bg-pink-500 rounded-full border border-black transform -rotate-15 shadow-lg relative">
            <div className="absolute inset-0 bg-gradient-to-br from-pink-400 to-pink-600 rounded-full"></div>
          </div>
          <div className="absolute -right-2 sm:-right-4 top-0 w-4 h-4 sm:w-6 sm:h-6 bg-pink-500 rounded-full border border-black transform rotate-15 shadow-lg relative">
            <div className="absolute inset-0 bg-gradient-to-bl from-pink-400 to-pink-600 rounded-full"></div>
          </div>
        </div>
      </div>
      
      <div className="absolute bottom-1/2 left-1/3 sm:bottom-1/3 sm:left-1/2 animate-float-bow opacity-50" style={{animationDelay: '3s'}}>
        <div className="w-6 h-6 sm:w-10 sm:h-10 relative">
          <div className="w-2 h-3 sm:w-4 sm:h-5 bg-pink-500 rounded-sm border border-black mx-auto shadow-lg relative">
            <div className="absolute inset-0 bg-gradient-to-b from-pink-400 to-pink-600 rounded-sm"></div>
          </div>
          <div className="absolute -left-1.5 sm:-left-3 top-0 w-3 h-3 sm:w-5 sm:h-5 bg-pink-500 rounded-full border border-black transform -rotate-15 shadow-lg relative">
            <div className="absolute inset-0 bg-gradient-to-br from-pink-400 to-pink-600 rounded-full"></div>
          </div>
          <div className="absolute -right-1.5 sm:-right-3 top-0 w-3 h-3 sm:w-5 sm:h-5 bg-pink-500 rounded-full border border-black transform rotate-15 shadow-lg relative">
            <div className="absolute inset-0 bg-gradient-to-bl from-pink-400 to-pink-600 rounded-full"></div>
          </div>
        </div>
      </div>
      
      {/* Mobile-friendly kawaii symbols - Background layer */}
      <div className="absolute top-28 left-2/3 sm:top-48 sm:left-2/3 text-pink-400 text-lg sm:text-2xl animate-gentle-bounce opacity-40" style={{animationDelay: '1.2s'}}>
        🎀
      </div>
      
      <div className="absolute bottom-28 right-2/3 sm:bottom-48 sm:right-2/3 text-red-400 text-base sm:text-xl animate-gentle-bounce opacity-40" style={{animationDelay: '2.8s'}}>
        💖
      </div>
      
      <div className="absolute top-3/4 left-1/4 text-pink-300 text-sm sm:text-lg animate-gentle-bounce opacity-40" style={{animationDelay: '4.2s'}}>
        🌸
      </div>
      
      <div className="absolute bottom-1/4 right-1/4 text-yellow-400 text-base sm:text-xl animate-gentle-bounce opacity-40" style={{animationDelay: '3.7s'}}>
        🌟
      </div>
    </div>
  );
};