import React, { useState, useEffect } from 'react';
import { Download, X, Smartphone, Monitor, Apple, Chrome } from 'lucide-react';
import { HelloKittySticker } from './HelloKittySticker';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

// Extend Navigator interface for PWA features
declare global {
  interface Navigator {
    standalone?: boolean;
  }
}

export const PWAInstallPrompt: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  const [installSource, setInstallSource] = useState<'browser' | 'ios' | 'android' | 'desktop'>('browser');

  useEffect(() => {
    // Detect platform
    const userAgent = window.navigator.userAgent.toLowerCase();
    const isIOSDevice = /ipad|iphone|ipod/.test(userAgent);
    const isInStandaloneMode = window.navigator.standalone === true;
    const isInAppBrowser = window.matchMedia('(display-mode: standalone)').matches;

    setIsIOS(isIOSDevice);
    setIsStandalone(isInStandaloneMode || isInAppBrowser);

    // Determine install source
    if (isIOSDevice) {
      setInstallSource('ios');
    } else if (/android/.test(userAgent)) {
      setInstallSource('android');
    } else if (/windows|macintosh|linux/.test(userAgent)) {
      setInstallSource('desktop');
    }

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // Show custom prompt after delay for better UX
      setTimeout(() => {
        setShowPrompt(true);
      }, 3000);
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      console.log('PWA was installed');
      setShowPrompt(false);
      setDeferredPrompt(null);
      
      // Show success message
      setTimeout(() => {
        alert('🎉 Wicky and Batman Stories installed successfully! You can now find it on your home screen.');
      }, 500);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // For iOS, show prompt if not in standalone mode and not dismissed recently
    if (isIOSDevice && !isInStandaloneMode && !localStorage.getItem('ios-install-dismissed')) {
      setTimeout(() => {
        setShowPrompt(true);
      }, 5000);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      try {
        deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        
        if (outcome === 'accepted') {
          console.log('User accepted the install prompt');
        } else {
          console.log('User dismissed the install prompt');
        }
        
        setDeferredPrompt(null);
        setShowPrompt(false);
      } catch (error) {
        console.error('Error showing install prompt:', error);
      }
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    
    // For iOS, remember dismissal
    if (isIOS) {
      localStorage.setItem('ios-install-dismissed', Date.now().toString());
    }
  };

  const getInstallInstructions = () => {
    switch (installSource) {
      case 'ios':
        return {
          title: '🌸 Install Wicky Stories on iOS',
          steps: [
            '1. Tap the Share button',
            '2. Scroll and tap "Add to Home Screen"',
            '3. Tap "Add" to confirm'
          ],
          icon: <Apple className="w-6 h-6" />
        };
      case 'android':
        return {
          title: '🎀 Install Wicky Stories on Android',
          steps: [
            '1. Tap "Install" when prompted',
            '2. Or tap menu ⋮ > "Install app"',
            '3. Confirm installation'
          ],
          icon: <Smartphone className="w-6 h-6" />
        };
      case 'desktop':
        return {
          title: '✨ Install Wicky Stories on Desktop',
          steps: [
            '1. Click the install icon in address bar',
            '2. Or use browser menu > "Install app"',
            '3. App will open in its own window'
          ],
          icon: <Monitor className="w-6 h-6" />
        };
      default:
        return {
          title: '💖 Install Wicky Stories',
          steps: [
            '1. Look for install option in your browser',
            '2. Follow the prompts to install',
            '3. Enjoy the app experience!'
          ],
          icon: <Download className="w-6 h-6" />
        };
    }
  };

  // Don't show if already installed or dismissed
  if (isStandalone || !showPrompt) {
    return null;
  }

  const installInfo = getInstallInstructions();

  return (
    <div className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[100] flex items-center justify-center p-4">
      <div className="bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl border-2 border-pink-200/60 max-w-md w-full relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-4 left-4 text-lg animate-sparkle text-pink-400 opacity-40" style={{animationDelay: '0s'}}>
            ✨
          </div>
          <div className="absolute bottom-4 right-4 text-sm animate-sparkle text-blue-400 opacity-30" style={{animationDelay: '2s'}}>
            🌟
          </div>
          <div className="absolute top-4 right-1/3 text-xs animate-sparkle text-yellow-400 opacity-35" style={{animationDelay: '4s'}}>
            💫
          </div>
        </div>

        {/* Close button */}
        <button
          onClick={handleDismiss}
          className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors z-10"
        >
          <X className="w-5 h-5" />
        </button>

        <div className="p-8 relative z-10">
          {/* Header with Hello Kitty */}
          <div className="text-center mb-6">
            <div className="flex items-center justify-center mb-4">
              <div className="mr-3">
                <HelloKittySticker size="medium" pose="waving" />
              </div>
              <div>
                {installInfo.icon}
              </div>
            </div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-pink-500 to-blue-500 bg-clip-text text-transparent mb-2">
              {installInfo.title}
            </h2>
            <p className="text-gray-600 text-sm">
              Get the full app experience with offline access and faster loading!
            </p>
          </div>

          {/* Installation steps */}
          <div className="space-y-3 mb-6">
            {installInfo.steps.map((step, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-pink-100 text-pink-600 rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                  {index + 1}
                </div>
                <p className="text-gray-700 text-sm leading-relaxed">{step}</p>
              </div>
            ))}
          </div>

          {/* Benefits */}
          <div className="bg-gradient-to-r from-pink-50 to-blue-50 rounded-2xl p-4 mb-6">
            <h3 className="font-semibold text-gray-800 mb-2 text-center">✨ App Benefits</h3>
            <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div className="flex items-center space-x-2">
                <span>🚀</span>
                <span>Faster loading</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>📱</span>
                <span>Home screen access</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>🔄</span>
                <span>Offline support</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>💾</span>
                <span>Auto-sync</span>
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex space-x-3">
            {deferredPrompt && installSource !== 'ios' ? (
              <button
                onClick={handleInstallClick}
                className="flex-1 bg-gradient-to-r from-pink-400 to-blue-500 text-white font-semibold py-3 px-6 rounded-2xl hover:from-pink-500 hover:to-blue-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
              >
                <Download className="w-5 h-5 inline mr-2" />
                Install Now
              </button>
            ) : (
              <div className="flex-1 bg-gradient-to-r from-pink-100 to-blue-100 text-gray-700 font-medium py-3 px-6 rounded-2xl text-center">
                Follow steps above ☝️
              </div>
            )}
            
            <button
              onClick={handleDismiss}
              className="px-6 py-3 text-gray-600 font-medium rounded-2xl hover:bg-gray-100 transition-colors"
            >
              Maybe Later
            </button>
          </div>

          {/* Platform-specific note */}
          {isIOS && (
            <p className="text-xs text-gray-500 text-center mt-4">
              💡 On iOS, you need to use Safari browser to install apps
            </p>
          )}
        </div>
      </div>
    </div>
  );
};