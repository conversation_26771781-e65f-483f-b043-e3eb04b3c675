import React from 'react';

interface HelloKittyProps {
  size?: 'small' | 'medium' | 'large' | 'giant';
  className?: string;
  animated?: boolean;
  pose?: 'classic' | 'winking' | 'happy' | 'sleepy' | 'waving';
}

export const HelloKittySticker: React.FC<HelloKittyProps> = ({ 
  size = 'medium', 
  className = '',
  animated = true,
  pose = 'classic'
}) => {
  const sizeClasses = {
    small: 'w-16 h-20',
    medium: 'w-20 h-24',
    large: 'w-24 h-28',
    giant: 'w-48 h-56'
  };

  const animationClass = animated ? 'animate-float' : '';

  const renderEyes = () => {
    switch (pose) {
      case 'winking':
        return (
          <>
            <div className="absolute top-[35%] left-[35%] w-2 h-2 bg-black rounded-full"></div>
            <div className="absolute top-[37%] right-[35%] w-3 h-0.5 bg-black rounded-full transform rotate-12"></div>
          </>
        );
      case 'happy':
        return (
          <>
            <div className="absolute top-[35%] left-[35%] w-2 h-2 bg-black rounded-full"></div>
            <div className="absolute top-[35%] right-[35%] w-2 h-2 bg-black rounded-full"></div>
          </>
        );
      case 'sleepy':
        return (
          <>
            <div className="absolute top-[37%] left-[35%] w-3 h-0.5 bg-black rounded-full"></div>
            <div className="absolute top-[37%] right-[35%] w-3 h-0.5 bg-black rounded-full"></div>
          </>
        );
      default:
        return (
          <>
            <div className="absolute top-[35%] left-[35%] w-2 h-2 bg-black rounded-full"></div>
            <div className="absolute top-[35%] right-[35%] w-2 h-2 bg-black rounded-full"></div>
          </>
        );
    }
  };

  const renderArms = () => {
    if (pose === 'waving') {
      return (
        <>
          {/* Left arm - normal position */}
          <div className="absolute top-[65%] left-[15%] w-[20%] h-[25%] bg-white border-2 border-black rounded-full transform rotate-45 shadow-md"></div>
          {/* Right arm - waving up */}
          <div className="absolute top-[55%] right-[15%] w-[20%] h-[25%] bg-white border-2 border-black rounded-full transform -rotate-30 shadow-md animate-wave"></div>
        </>
      );
    } else {
      return (
        <>
          {/* Left arm */}
          <div className="absolute top-[65%] left-[15%] w-[20%] h-[25%] bg-white border-2 border-black rounded-full transform rotate-45 shadow-md"></div>
          {/* Right arm */}
          <div className="absolute top-[65%] right-[15%] w-[20%] h-[25%] bg-white border-2 border-black rounded-full transform -rotate-45 shadow-md"></div>
        </>
      );
    }
  };

  return (
    <div className={`${sizeClasses[size]} ${className} ${animationClass} relative`}>
      {/* AUTHENTIC Hello Kitty - EXACTLY like reference image */}
      <div className="w-full h-full relative">
        
        {/* Main body outline - white with black border */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-full h-full bg-white border-4 border-black relative overflow-visible shadow-xl" style={{borderRadius: '45% 45% 25% 25%'}}>
          
          {/* Head section - top 60% */}
          <div className="absolute top-0 left-0 w-full h-[60%] relative">
            
            {/* Left ear */}
            <div className="absolute -top-[8%] left-[20%] w-[25%] h-[35%] bg-white border-4 border-black rounded-full transform -rotate-15 shadow-lg"></div>
            
            {/* Right ear */}
            <div className="absolute -top-[8%] right-[20%] w-[25%] h-[35%] bg-white border-4 border-black rounded-full transform rotate-15 shadow-lg"></div>
            
            {/* PINK bow on left ear - EXACTLY like reference */}
            <div className="absolute -top-[5%] left-[18%] transform -translate-x-1/2">
              <div className="relative animate-bow-wiggle">
                {/* Bow center knot */}
                <div className="w-3 h-4 bg-pink-500 border-2 border-black shadow-md relative" style={{borderRadius: '20% 20% 40% 40%'}}>
                  <div className="absolute inset-0 bg-gradient-to-b from-pink-400 to-pink-600" style={{borderRadius: '20% 20% 40% 40%'}}></div>
                </div>
                {/* Bow left wing */}
                <div className="absolute -left-3 top-0 w-4 h-4 bg-pink-500 border-2 border-black transform -rotate-15 shadow-md relative" style={{borderRadius: '50% 20% 50% 20%'}}>
                  <div className="absolute inset-0 bg-gradient-to-br from-pink-400 to-pink-600" style={{borderRadius: '50% 20% 50% 20%'}}></div>
                </div>
                {/* Bow right wing */}
                <div className="absolute -right-3 top-0 w-4 h-4 bg-pink-500 border-2 border-black transform rotate-15 shadow-md relative" style={{borderRadius: '20% 50% 20% 50%'}}>
                  <div className="absolute inset-0 bg-gradient-to-bl from-pink-400 to-pink-600" style={{borderRadius: '20% 50% 20% 50%'}}></div>
                </div>
              </div>
            </div>
            
            {/* Eyes - EXACTLY like Hello Kitty */}
            {renderEyes()}
            
            {/* Yellow oval nose - signature Hello Kitty feature */}
            <div className="absolute top-[45%] left-1/2 transform -translate-x-1/2 w-2 h-1.5 bg-yellow-400 border border-black shadow-sm" style={{borderRadius: '50%'}}>
              <div className="absolute inset-0 bg-gradient-to-b from-yellow-300 to-yellow-500" style={{borderRadius: '50%'}}></div>
            </div>
            
            {/* Whiskers - 3 on each side */}
            <div className="absolute top-[47%] left-[15%] w-[25%] h-0.5 bg-black transform -translate-y-1"></div>
            <div className="absolute top-[47%] left-[15%] w-[22%] h-0.5 bg-black"></div>
            <div className="absolute top-[47%] left-[15%] w-[20%] h-0.5 bg-black transform translate-y-1"></div>
            
            <div className="absolute top-[47%] right-[15%] w-[25%] h-0.5 bg-black transform -translate-y-1"></div>
            <div className="absolute top-[47%] right-[15%] w-[22%] h-0.5 bg-black"></div>
            <div className="absolute top-[47%] right-[15%] w-[20%] h-0.5 bg-black transform translate-y-1"></div>
          </div>
          
          {/* PINK dress/overalls - EXACTLY like reference */}
          <div className="absolute top-[55%] left-1/2 transform -translate-x-1/2 w-[70%] h-[40%] bg-pink-500 border-3 border-black shadow-lg relative" style={{borderRadius: '15% 15% 20% 20%'}}>
            <div className="absolute inset-0 bg-gradient-to-b from-pink-400 to-pink-600" style={{borderRadius: '15% 15% 20% 20%'}}></div>
          </div>
          
          {/* Arms extending from body */}
          {renderArms()}
          
          {/* White legs */}
          <div className="absolute bottom-[5%] left-[30%] w-[15%] h-[20%] bg-white border-2 border-black rounded-full shadow-md"></div>
          <div className="absolute bottom-[5%] right-[30%] w-[15%] h-[20%] bg-white border-2 border-black rounded-full shadow-md"></div>
          
          {/* White feet */}
          <div className="absolute bottom-[-5%] left-[28%] w-[20%] h-[12%] bg-white border-2 border-black shadow-md" style={{borderRadius: '50% 50% 30% 30%'}}></div>
          <div className="absolute bottom-[-5%] right-[28%] w-[20%] h-[12%] bg-white border-2 border-black shadow-md" style={{borderRadius: '50% 50% 30% 30%'}}></div>
        </div>
      </div>
    </div>
  );
};

export const HelloKittyGiant: React.FC = () => {
  return (
    <div className="fixed bottom-8 right-8 animate-gentle-sway opacity-30 hover:opacity-60 transition-opacity duration-500">
      <div className="relative w-80 h-96 lg:w-96 lg:h-[32rem]">
        
        {/* GIANT Hello Kitty - EXACTLY like reference image - Background layer */}
        <div className="w-full h-full relative">
          
          {/* Main body outline - white with black border */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-full h-full bg-white border-4 lg:border-6 border-black relative overflow-visible shadow-2xl" style={{borderRadius: '45% 45% 25% 25%'}}>
            
            {/* Head section - top 60% */}
            <div className="absolute top-0 left-0 w-full h-[60%] relative">
              
              {/* Left ear */}
              <div className="absolute -top-[8%] left-[20%] w-[25%] h-[35%] bg-white border-4 lg:border-6 border-black rounded-full transform -rotate-15 shadow-2xl"></div>
              
              {/* Right ear */}
              <div className="absolute -top-[8%] right-[20%] w-[25%] h-[35%] bg-white border-4 lg:border-6 border-black rounded-full transform rotate-15 shadow-2xl"></div>
              
              {/* PINK bow on left ear - EXACTLY like reference */}
              <div className="absolute -top-[5%] left-[18%] transform -translate-x-1/2">
                <div className="relative animate-bow-wiggle">
                  {/* Bow center knot */}
                  <div className="w-8 h-12 lg:w-12 lg:h-16 bg-pink-500 border-3 lg:border-4 border-black shadow-xl relative" style={{borderRadius: '20% 20% 40% 40%'}}>
                    <div className="absolute inset-0 bg-gradient-to-b from-pink-400 via-pink-500 to-pink-600" style={{borderRadius: '20% 20% 40% 40%'}}></div>
                    <div className="absolute top-1 lg:top-2 left-1/2 transform -translate-x-1/2 w-2 lg:w-3 h-6 lg:h-10 bg-pink-300 opacity-60" style={{borderRadius: '20% 20% 40% 40%'}}></div>
                  </div>
                  {/* Bow left wing */}
                  <div className="absolute -left-8 lg:-left-12 top-0 w-12 h-12 lg:w-16 lg:h-16 bg-pink-500 border-3 lg:border-4 border-black transform -rotate-15 shadow-xl relative" style={{borderRadius: '50% 20% 50% 20%'}}>
                    <div className="absolute inset-0 bg-gradient-to-br from-pink-400 via-pink-500 to-pink-600" style={{borderRadius: '50% 20% 50% 20%'}}></div>
                    <div className="absolute top-2 lg:top-3 left-2 lg:left-3 w-4 lg:w-6 h-4 lg:h-6 bg-pink-300 opacity-60" style={{borderRadius: '50% 20% 50% 20%'}}></div>
                  </div>
                  {/* Bow right wing */}
                  <div className="absolute -right-8 lg:-right-12 top-0 w-12 h-12 lg:w-16 lg:h-16 bg-pink-500 border-3 lg:border-4 border-black transform rotate-15 shadow-xl relative" style={{borderRadius: '20% 50% 20% 50%'}}>
                    <div className="absolute inset-0 bg-gradient-to-bl from-pink-400 via-pink-500 to-pink-600" style={{borderRadius: '20% 50% 20% 50%'}}></div>
                    <div className="absolute top-2 lg:top-3 right-2 lg:right-3 w-4 lg:w-6 h-4 lg:h-6 bg-pink-300 opacity-60" style={{borderRadius: '20% 50% 20% 50%'}}></div>
                  </div>
                </div>
              </div>
              
              {/* Eyes - EXACTLY like Hello Kitty */}
              <div className="absolute top-[35%] left-[35%] w-6 lg:w-8 h-6 lg:h-8 bg-black rounded-full animate-blink shadow-lg"></div>
              <div className="absolute top-[35%] right-[35%] w-6 lg:w-8 h-6 lg:h-8 bg-black rounded-full animate-blink shadow-lg" style={{animationDelay: '0.1s'}}></div>
              
              {/* Eye highlights */}
              <div className="absolute top-[33%] left-[36%] w-1.5 lg:w-2 h-1.5 lg:h-2 bg-white rounded-full opacity-90"></div>
              <div className="absolute top-[33%] right-[36%] w-1.5 lg:w-2 h-1.5 lg:h-2 bg-white rounded-full opacity-90"></div>
              
              {/* Yellow oval nose - signature Hello Kitty feature */}
              <div className="absolute top-[45%] left-1/2 transform -translate-x-1/2 w-6 lg:w-8 h-4 lg:h-6 bg-yellow-400 border-2 lg:border-3 border-black shadow-lg" style={{borderRadius: '50%'}}>
                <div className="absolute inset-0 bg-gradient-to-b from-yellow-300 via-yellow-400 to-yellow-500" style={{borderRadius: '50%'}}></div>
                <div className="absolute top-0.5 lg:top-1 left-1 lg:left-2 w-1.5 lg:w-2 h-1.5 lg:h-2 bg-yellow-200 rounded-full opacity-80"></div>
              </div>
              
              {/* Whiskers - 3 on each side */}
              <div className="absolute top-[47%] left-[15%] w-[25%] h-0.5 lg:h-1 bg-black transform -translate-y-1 lg:-translate-y-2 rounded-full shadow-sm"></div>
              <div className="absolute top-[47%] left-[15%] w-[22%] h-0.5 lg:h-1 bg-black rounded-full shadow-sm"></div>
              <div className="absolute top-[47%] left-[15%] w-[20%] h-0.5 lg:h-1 bg-black transform translate-y-1 lg:translate-y-2 rounded-full shadow-sm"></div>
              
              <div className="absolute top-[47%] right-[15%] w-[25%] h-0.5 lg:h-1 bg-black transform -translate-y-1 lg:-translate-y-2 rounded-full shadow-sm"></div>
              <div className="absolute top-[47%] right-[15%] w-[22%] h-0.5 lg:h-1 bg-black rounded-full shadow-sm"></div>
              <div className="absolute top-[47%] right-[15%] w-[20%] h-0.5 lg:h-1 bg-black transform translate-y-1 lg:translate-y-2 rounded-full shadow-sm"></div>
            </div>
            
            {/* PINK dress/overalls - EXACTLY like reference */}
            <div className="absolute top-[55%] left-1/2 transform -translate-x-1/2 w-[70%] h-[40%] bg-pink-500 border-3 lg:border-4 border-black shadow-2xl relative" style={{borderRadius: '15% 15% 20% 20%'}}>
              <div className="absolute inset-0 bg-gradient-to-b from-pink-400 via-pink-500 to-pink-600" style={{borderRadius: '15% 15% 20% 20%'}}></div>
              <div className="absolute top-1 lg:top-2 left-1 lg:left-2 w-[20%] h-[15%] bg-pink-300 opacity-50" style={{borderRadius: '15% 15% 20% 20%'}}></div>
              <div className="absolute bottom-1 lg:bottom-2 right-1 lg:right-2 w-[25%] h-[20%] bg-pink-600 opacity-40" style={{borderRadius: '15% 15% 20% 20%'}}></div>
            </div>
            
            {/* Arms extending from body */}
            <div className="absolute top-[65%] left-[15%] w-[20%] h-[25%] bg-white border-3 lg:border-4 border-black rounded-full transform rotate-45 shadow-xl"></div>
            <div className="absolute top-[65%] right-[15%] w-[20%] h-[25%] bg-white border-3 lg:border-4 border-black rounded-full transform -rotate-45 shadow-xl animate-gentle-wave"></div>
            
            {/* White legs */}
            <div className="absolute bottom-[5%] left-[30%] w-[15%] h-[20%] bg-white border-3 lg:border-4 border-black rounded-full shadow-xl"></div>
            <div className="absolute bottom-[5%] right-[30%] w-[15%] h-[20%] bg-white border-3 lg:border-4 border-black rounded-full shadow-xl"></div>
            
            {/* White feet */}
            <div className="absolute bottom-[-5%] left-[28%] w-[20%] h-[12%] bg-white border-3 lg:border-4 border-black shadow-xl" style={{borderRadius: '50% 50% 30% 30%'}}></div>
            <div className="absolute bottom-[-5%] right-[28%] w-[20%] h-[12%] bg-white border-3 lg:border-4 border-black shadow-xl" style={{borderRadius: '50% 50% 30% 30%'}}></div>
          </div>
        </div>
        
        {/* Floating hearts around Hello Kitty - Background layer */}
        <div className="absolute -top-6 lg:-top-8 -left-6 lg:-left-8 text-pink-500 text-2xl lg:text-4xl animate-float-heart opacity-60">
          ♥
        </div>
        <div className="absolute -top-8 lg:-top-12 right-6 lg:right-8 text-red-500 text-xl lg:text-3xl animate-float-heart opacity-60" style={{animationDelay: '1s'}}>
          ♥
        </div>
        <div className="absolute bottom-6 lg:bottom-8 -left-8 lg:-left-12 text-pink-400 text-lg lg:text-2xl animate-float-heart opacity-60" style={{animationDelay: '2s'}}>
          ♥
        </div>
        
        {/* Sparkle effects - Background layer */}
        <div className="absolute -top-4 lg:-top-6 left-12 lg:left-16 text-yellow-400 text-xl lg:text-3xl animate-sparkle opacity-50">
          ✨
        </div>
        <div className="absolute bottom-12 lg:bottom-16 right-4 lg:right-6 text-yellow-300 text-lg lg:text-2xl animate-sparkle opacity-50" style={{animationDelay: '0.8s'}}>
          ⭐
        </div>
      </div>
    </div>
  );
};