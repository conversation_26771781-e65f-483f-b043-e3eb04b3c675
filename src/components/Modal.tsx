import React from 'react';
import { X, AlertTriangle, CheckCircle, Info, Heart } from 'lucide-react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  title: string;
  message: string;
  type?: 'info' | 'warning' | 'error' | 'success' | 'confirm';
  confirmText?: string;
  cancelText?: string;
  showCancel?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  type = 'info',
  confirmText = 'OK',
  cancelText = 'Cancel',
  showCancel = false
}) => {
  if (!isOpen) return null;

  const getTypeStyles = () => {
    switch (type) {
      case 'warning':
        return {
          icon: <AlertTriangle className="w-6 h-6" />,
          iconBg: 'bg-gradient-to-br from-yellow-400 to-orange-500',
          iconColor: 'text-white',
          borderColor: 'border-yellow-200',
          bgGradient: 'from-yellow-50/80 to-orange-50/80',
          confirmBg: 'from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600'
        };
      case 'error':
        return {
          icon: <X className="w-6 h-6" />,
          iconBg: 'bg-gradient-to-br from-red-400 to-red-500',
          iconColor: 'text-white',
          borderColor: 'border-red-200',
          bgGradient: 'from-red-50/80 to-pink-50/80',
          confirmBg: 'from-red-400 to-red-500 hover:from-red-500 hover:to-red-600'
        };
      case 'success':
        return {
          icon: <CheckCircle className="w-6 h-6" />,
          iconBg: 'bg-gradient-to-br from-green-400 to-green-500',
          iconColor: 'text-white',
          borderColor: 'border-green-200',
          bgGradient: 'from-green-50/80 to-emerald-50/80',
          confirmBg: 'from-green-400 to-green-500 hover:from-green-500 hover:to-green-600'
        };
      case 'confirm':
        return {
          icon: <Heart className="w-6 h-6" />,
          iconBg: 'bg-gradient-to-br from-pink-400 to-pink-500',
          iconColor: 'text-white',
          borderColor: 'border-pink-200',
          bgGradient: 'from-pink-50/80 to-blue-50/80',
          confirmBg: 'from-pink-400 to-pink-500 hover:from-pink-500 hover:to-pink-600'
        };
      default:
        return {
          icon: <Info className="w-6 h-6" />,
          iconBg: 'bg-gradient-to-br from-blue-400 to-blue-500',
          iconColor: 'text-white',
          borderColor: 'border-blue-200',
          bgGradient: 'from-blue-50/80 to-indigo-50/80',
          confirmBg: 'from-blue-400 to-blue-500 hover:from-blue-500 hover:to-blue-600'
        };
    }
  };

  const styles = getTypeStyles();

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    } else {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center p-4 z-[100] safe-area animate-in fade-in duration-200"
      onClick={handleBackdropClick}
    >
      <div className={`bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl w-full max-w-md border ${styles.borderColor} animate-in zoom-in-95 duration-300`}>
        {/* Header with Icon */}
        <div className={`p-6 border-b ${styles.borderColor} bg-gradient-to-r ${styles.bgGradient}`}>
          <div className="flex items-center space-x-4">
            <div className={`w-12 h-12 rounded-full ${styles.iconBg} ${styles.iconColor} flex items-center justify-center shadow-lg animate-gentle-bounce`}>
              {styles.icon}
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-gray-700 leading-relaxed text-sm sm:text-base">
            {message}
          </p>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 p-6 pt-0">
          {showCancel && (
            <button
              onClick={onClose}
              className="px-4 py-2.5 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-xl transition-all duration-200 font-medium text-sm sm:text-base"
            >
              {cancelText}
            </button>
          )}
          <button
            onClick={handleConfirm}
            className={`px-6 py-2.5 bg-gradient-to-r ${styles.confirmBg} text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl font-medium text-sm sm:text-base`}
          >
            {confirmText}
          </button>
        </div>

        {/* Decorative elements */}
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br from-pink-300 to-pink-400 rounded-full opacity-60 transform rotate-45"></div>
        <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-gradient-to-br from-pink-400 to-pink-500 rounded-full opacity-80 transform rotate-45"></div>
      </div>
    </div>
  );
};