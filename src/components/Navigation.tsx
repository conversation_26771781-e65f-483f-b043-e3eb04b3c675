import React from 'react';
import { Search, Plus, Heart, Sparkles } from 'lucide-react';

interface NavigationProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onNewNote: () => void;
  notesCount: number;
}

export const Navigation: React.FC<NavigationProps> = ({ 
  searchTerm, 
  onSearchChange, 
  onNewNote, 
  notesCount 
}) => {
  return (
    <div className="sticky top-0 z-40 backdrop-blur-md bg-gradient-to-r from-pink-50/90 via-white/90 to-blue-50/90 border-b border-pink-100/50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          {/* Logo and title */}
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Heart className="w-7 h-7 sm:w-8 sm:h-8 text-pink-400 fill-current" />
              <div className="absolute -top-0.5 -right-0.5 w-2 h-2 sm:w-2.5 sm:h-2.5 bg-gradient-to-br from-pink-400 to-pink-500 rounded-full transform rotate-45"></div>
            </div>
            <div className="hidden sm:flex items-center space-x-2">
              <h1 className="text-lg font-semibold text-gray-800">Batman & Wicky's Notes</h1>
              <div className="flex items-center space-x-1">
                <Sparkles className="w-3 h-3 sm:w-3.5 sm:h-3.5 text-pink-400" />
                <span className="text-xs sm:text-sm text-gray-500 font-medium">{notesCount}</span>
              </div>
            </div>
            <div className="sm:hidden flex items-center space-x-1">
              <Sparkles className="w-3 h-3 text-pink-400" />
              <span className="text-xs text-gray-500 font-medium">{notesCount}</span>
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex items-center space-x-2 sm:space-x-3">
            {/* Search button for mobile */}
            <button className="sm:hidden p-2.5 text-gray-500 hover:text-gray-700 hover:bg-white/60 rounded-xl transition-all duration-200 border border-pink-100/50">
              <Search className="w-5 h-5" />
            </button>
            
            {/* Search bar for larger screens */}
            <div className="relative hidden sm:block">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search notes..."
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 pr-4 py-2.5 w-48 md:w-56 lg:w-64 bg-white/70 backdrop-blur-sm border border-pink-100/50 rounded-xl text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-200 focus:border-pink-300 transition-all duration-300"
              />
            </div>
            
            {/* New note button with PWA shortcut data attribute */}
            <button
              onClick={onNewNote}
              data-action="new-note"
              className="flex items-center space-x-1.5 sm:space-x-2 px-3 py-2.5 sm:px-4 bg-gradient-to-r from-pink-400 to-pink-500 text-white rounded-xl hover:from-pink-500 hover:to-pink-600 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 font-medium"
            >
              <Plus className="w-4 h-4 sm:w-4.5 sm:h-4.5" />
              <span className="text-sm sm:text-base">New</span>
            </button>
          </div>
        </div>
        
        {/* Mobile search bar - full width below */}
        <div className="mt-3 sm:hidden">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search your notes..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10 pr-4 py-3 w-full bg-white/70 backdrop-blur-sm border border-pink-100/50 rounded-xl text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-200 focus:border-pink-300 transition-all duration-300"
            />
          </div>
        </div>
      </div>
      
      {/* Decorative whiskers */}
      <div className="absolute bottom-0 left-1/4 w-8 sm:w-12 md:w-16 h-px bg-gradient-to-r from-transparent via-pink-200 to-transparent opacity-50"></div>
      <div className="absolute bottom-0 right-1/4 w-8 sm:w-12 md:w-16 h-px bg-gradient-to-r from-transparent via-blue-200 to-transparent opacity-50"></div>
    </div>
  );
};