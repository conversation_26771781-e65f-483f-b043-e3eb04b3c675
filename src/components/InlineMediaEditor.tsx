import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Plus, Image, Mic, Video, Camera, Play, Pause, X, Download, RotateCcw } from 'lucide-react';
import { VoiceRecorder } from './VoiceRecorder';
import { CameraCapture } from './CameraCapture';
import { MediaService } from '../services/mediaService';
import { MediaUtils } from '../utils/mediaUtils';
import type { NoteAttachment, MediaUploadProgress, InlineMediaElement } from '../types/media';

interface InlineMediaEditorProps {
  noteId: string | null;
  content: string;
  onContentChange: (content: string, mediaElements: InlineMediaElement[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

interface MediaBlock {
  id: string;
  type: 'text' | 'media';
  content?: string;
  mediaElement?: InlineMediaElement;
  order: number;
}

export const InlineMediaEditor: React.FC<InlineMediaEditorProps> = ({
  noteId,
  content,
  onContentChange,
  placeholder = "Start writing...",
  className = "",
  disabled = false
}) => {
  const [mediaElements, setMediaElements] = useState<InlineMediaElement[]>([]);
  const [uploadProgress, setUploadProgress] = useState<MediaUploadProgress[]>([]);
  const [showMediaMenu, setShowMediaMenu] = useState(false);
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map());
  const [draggedOver, setDraggedOver] = useState(false);

  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Parse content to extract media elements on mount
  useEffect(() => {
    if (noteId) {
      loadExistingMediaElements();
    }
  }, [noteId]);

  const loadExistingMediaElements = async () => {
    if (!noteId) return;
    
    try {
      const attachments = await MediaService.getAttachments(noteId);
      const elements: InlineMediaElement[] = attachments.map((attachment, index) => ({
        id: `media_${attachment.id}`,
        type: attachment.type,
        attachment,
        position: index * 100, // Spread out positions
      }));
      setMediaElements(elements);
    } catch (error) {
      console.error('Error loading media elements:', error);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDraggedOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDraggedOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setDraggedOver(false);
    
    if (!noteId || disabled) return;

    const files = Array.from(e.dataTransfer.files);
    for (const file of files) {
      await handleFileUpload(file);
    }
  };

  const handleFileSelect = (type: 'image' | 'video') => {
    if (type === 'image') {
      fileInputRef.current?.click();
    } else if (type === 'video') {
      videoInputRef.current?.click();
    }
  };

  const handleFileUpload = async (file: File) => {
    if (!noteId) return;

    let mediaType: 'image' | 'voice' | 'video';
    if (file.type.startsWith('image/')) {
      mediaType = 'image';
    } else if (file.type.startsWith('video/')) {
      mediaType = 'video';
    } else if (file.type.startsWith('audio/')) {
      mediaType = 'voice';
    } else {
      console.error('Unsupported file type:', file.type);
      return;
    }

    try {
      const attachment = await MediaService.uploadMedia(
        file,
        noteId,
        mediaType,
        (progress) => {
          setUploadProgress(prev => {
            const existing = prev.find(p => p.id === progress.id);
            if (existing) {
              return prev.map(p => p.id === progress.id ? progress : p);
            } else {
              return [...prev, progress];
            }
          });
        }
      );

      // Create new media element
      const newElement: InlineMediaElement = {
        id: `media_${attachment.id}`,
        type: mediaType,
        attachment,
        position: mediaElements.length * 100,
      };

      setMediaElements(prev => [...prev, newElement]);
      setUploadProgress(prev => prev.filter(p => p.id !== attachment.id));
      
      // Notify parent of changes
      onContentChange(content, [...mediaElements, newElement]);
    } catch (error) {
      console.error('Error uploading file:', error);
      setUploadProgress(prev => prev.filter(p => p.id === file.name));
    }
  };

  const handleVoiceRecording = async (audioBlob: Blob, duration: number, waveform: number[]) => {
    if (!noteId) return;

    try {
      const fileName = `voice-note-${Date.now()}.mp3`;
      const audioFile = new File([audioBlob], fileName, { type: 'audio/mp3' });
      
      const attachment = await MediaService.uploadMedia(
        audioFile,
        noteId,
        'voice',
        (progress) => {
          setUploadProgress(prev => {
            const existing = prev.find(p => p.id === progress.id);
            if (existing) {
              return prev.map(p => p.id === progress.id ? progress : p);
            } else {
              return [...prev, progress];
            }
          });
        },
        waveform
      );

      const newElement: InlineMediaElement = {
        id: `media_${attachment.id}`,
        type: 'voice',
        attachment,
        position: mediaElements.length * 100,
      };

      setMediaElements(prev => [...prev, newElement]);
      setUploadProgress(prev => prev.filter(p => p.id !== attachment.id));
      onContentChange(content, [...mediaElements, newElement]);
    } catch (error) {
      console.error('Error saving voice recording:', error);
    }
  };

  const handleDeleteMedia = async (elementId: string) => {
    const element = mediaElements.find(e => e.id === elementId);
    if (!element) return;

    try {
      await MediaService.deleteAttachment(element.attachment.id);
      const updatedElements = mediaElements.filter(e => e.id !== elementId);
      setMediaElements(updatedElements);
      onContentChange(content, updatedElements);
    } catch (error) {
      console.error('Error deleting media:', error);
    }
  };

  const handlePlayAudio = (attachment: NoteAttachment) => {
    const audioUrl = MediaService.getMediaUrl(attachment);
    
    // Stop currently playing audio
    if (playingAudio && playingAudio !== attachment.id) {
      const currentAudio = audioElements.get(playingAudio);
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }
    }

    let audio = audioElements.get(attachment.id);
    
    if (!audio) {
      audio = new Audio(audioUrl);
      audio.onended = () => setPlayingAudio(null);
      audio.onerror = () => {
        console.error('Error playing audio');
        setPlayingAudio(null);
      };
      setAudioElements(prev => new Map(prev).set(attachment.id, audio!));
    }

    if (playingAudio === attachment.id) {
      audio.pause();
      setPlayingAudio(null);
    } else {
      audio.play();
      setPlayingAudio(attachment.id);
    }
  };

  const renderMediaElement = (element: InlineMediaElement) => {
    const { attachment, type } = element;

    switch (type) {
      case 'image':
        return (
          <div className="relative group my-4 max-w-md mx-auto bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
            <img
              src={MediaService.getMediaUrl(attachment)}
              alt={attachment.file_name}
              className="w-full h-auto object-cover"
              style={{ maxHeight: '400px' }}
            />
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                onClick={() => handleDeleteMedia(element.id)}
                className="p-1.5 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors shadow-lg"
              >
                <X size={14} />
              </button>
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3 text-white text-sm opacity-0 group-hover:opacity-100 transition-opacity">
              <p className="truncate">{attachment.file_name}</p>
              <p className="text-xs opacity-75">{MediaUtils.formatFileSize(attachment.file_size)}</p>
            </div>
          </div>
        );

      case 'video':
        return (
          <div className="relative group my-4 max-w-md mx-auto bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
            <video
              src={MediaService.getMediaUrl(attachment)}
              controls
              className="w-full h-auto"
              style={{ maxHeight: '400px' }}
              preload="metadata"
            >
              Your browser does not support the video tag.
            </video>
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                onClick={() => handleDeleteMedia(element.id)}
                className="p-1.5 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors shadow-lg"
              >
                <X size={14} />
              </button>
            </div>
          </div>
        );

      case 'voice':
        return (
          <div className="relative group my-4 max-w-md mx-auto bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-4 border border-blue-100 shadow-sm">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => handlePlayAudio(attachment)}
                className="flex-shrink-0 w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-md"
              >
                {playingAudio === attachment.id ? <Pause size={18} /> : <Play size={18} />}
              </button>
              
              <div className="flex-grow min-w-0">
                <p className="font-medium text-blue-900 truncate text-sm">{attachment.file_name}</p>
                <div className="flex items-center space-x-2 text-xs text-blue-700 mt-1">
                  <span>{MediaUtils.formatDuration(attachment.duration_seconds || 0)}</span>
                  <span>•</span>
                  <span>{MediaUtils.formatFileSize(attachment.file_size)}</span>
                </div>
              </div>
              
              <button
                onClick={() => handleDeleteMedia(element.id)}
                className="flex-shrink-0 p-1.5 text-red-500 hover:text-red-600 hover:bg-red-100 rounded-lg opacity-0 group-hover:opacity-100 transition-all"
              >
                <X size={14} />
              </button>
            </div>
            
            {/* Waveform visualization */}
            {attachment.metadata?.waveform && (
              <div className="mt-3 flex items-end space-x-0.5 h-6">
                {attachment.metadata.waveform.map((amplitude: number, idx: number) => (
                  <div
                    key={idx}
                    className="bg-blue-400 rounded-full"
                    style={{
                      width: '2px',
                      height: `${Math.max(2, amplitude * 20)}px`
                    }}
                  />
                ))}
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Main Editor Area */}
      <div
        ref={editorRef}
        className={`min-h-[200px] p-4 border-2 rounded-2xl transition-all duration-200 ${
          draggedOver
            ? 'border-pink-400 bg-pink-50/50'
            : 'border-pink-100/40 bg-gradient-to-br from-white/40 via-pink-50/20 to-blue-50/20'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Text Content */}
        <textarea
          value={content}
          onChange={(e) => onContentChange(e.target.value, mediaElements)}
          placeholder={placeholder}
          className="w-full min-h-[150px] text-gray-700 leading-relaxed resize-none bg-transparent border-none outline-none placeholder-pink-400/70"
          style={{ fontSize: '16px' }}
          disabled={disabled}
        />

        {/* Media Elements */}
        {mediaElements.length > 0 && (
          <div className="mt-6 space-y-4">
            {mediaElements.map((element) => (
              <div key={element.id}>
                {renderMediaElement(element)}
              </div>
            ))}
          </div>
        )}

        {/* Upload Progress */}
        {uploadProgress.length > 0 && (
          <div className="mt-4 space-y-2">
            {uploadProgress.map((progress) => (
              <div key={progress.id} className="bg-white/80 rounded-lg p-3 border border-gray-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">
                    {progress.status === 'uploading' && 'Uploading...'}
                    {progress.status === 'processing' && 'Processing...'}
                    {progress.status === 'complete' && 'Complete!'}
                    {progress.status === 'error' && 'Error'}
                  </span>
                  <span className="text-sm text-gray-500">{progress.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      progress.status === 'error'
                        ? 'bg-red-400'
                        : progress.status === 'complete'
                        ? 'bg-green-400'
                        : 'bg-pink-400'
                    }`}
                    style={{ width: `${progress.progress}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Drag & Drop Overlay */}
        {draggedOver && (
          <div className="absolute inset-0 border-4 border-dashed border-pink-400 bg-pink-50/90 rounded-2xl flex items-center justify-center pointer-events-none">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-pink-100 rounded-full flex items-center justify-center">
                <Plus className="w-8 h-8 text-pink-500" />
              </div>
              <p className="text-pink-600 font-medium text-lg">Drop your media here</p>
              <p className="text-pink-500 text-sm mt-1">Photos, videos, and audio files</p>
            </div>
          </div>
        )}
      </div>

      {/* Media Controls */}
      {!disabled && (
        <div className="mt-4 flex items-center space-x-2">
          <div className="relative">
            <button
              onClick={() => setShowMediaMenu(!showMediaMenu)}
              className="flex items-center space-x-2 px-3 py-2 bg-pink-100 text-pink-700 rounded-lg hover:bg-pink-200 transition-colors border border-pink-200"
            >
              <Plus size={16} />
              <span>Add Media</span>
            </button>

            {showMediaMenu && (
              <div className="absolute bottom-full left-0 mb-2 bg-white rounded-lg shadow-xl border border-gray-200 p-2 min-w-[180px] z-10">
                <button
                  onClick={() => {
                    handleFileSelect('image');
                    setShowMediaMenu(false);
                  }}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg"
                >
                  <Image size={16} />
                  <span>Photos</span>
                </button>
                <button
                  onClick={() => {
                    handleFileSelect('video');
                    setShowMediaMenu(false);
                  }}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg"
                >
                  <Video size={16} />
                  <span>Videos</span>
                </button>
                <button
                  onClick={() => {
                    if (noteId) {
                      setShowVoiceRecorder(true);
                      setShowMediaMenu(false);
                    }
                  }}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg"
                >
                  <Mic size={16} />
                  <span>Voice Note</span>
                </button>
              </div>
            )}
          </div>

          <CameraCapture
            onImageSelect={(files) => {
              files.forEach(handleFileUpload);
            }}
            disabled={!noteId}
            maxImages={5}
          />
        </div>
      )}

      {/* Hidden File Inputs */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={(e) => {
          if (e.target.files) {
            Array.from(e.target.files).forEach(handleFileUpload);
          }
          e.target.value = '';
        }}
        className="hidden"
      />
      <input
        ref={videoInputRef}
        type="file"
        accept="video/*"
        onChange={(e) => {
          if (e.target.files?.[0]) {
            handleFileUpload(e.target.files[0]);
          }
          e.target.value = '';
        }}
        className="hidden"
      />

      {/* Voice Recorder Modal */}
      <VoiceRecorder
        isOpen={showVoiceRecorder}
        onRecordingComplete={handleVoiceRecording}
        onCancel={() => setShowVoiceRecorder(false)}
        maxDuration={600}
      />
    </div>
  );
}; 