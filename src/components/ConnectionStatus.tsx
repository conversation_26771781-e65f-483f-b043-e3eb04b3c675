import React from 'react';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';

interface ConnectionStatusProps {
  isConnected: boolean;
  onRefresh: () => void;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ 
  isConnected, 
  onRefresh 
}) => {
  // Only show if explicitly disconnected for more than a few seconds
  // This prevents flickering connection status
  if (isConnected) return null;

  return (
    <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 animate-in slide-in-from-top duration-300">
      <div className="bg-blue-500/95 backdrop-blur-md text-white px-4 py-2 rounded-full shadow-lg border border-blue-400/50 flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">Syncing...</span>
        </div>
        <button
          onClick={onRefresh}
          className="p-1 hover:bg-blue-600/50 rounded-full transition-colors"
          title="Force refresh"
        >
          <RefreshCw className="w-3 h-3" />
        </button>
      </div>
    </div>
  );
};