import React, { useState, useEffect } from 'react';
import { HelloKittySticker } from './HelloKittySticker';

export const WelcomeKitty: React.FC = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [animationPhase, setAnimationPhase] = useState<'enter' | 'welcome' | 'fadeOut' | 'exit'>('enter');

  useEffect(() => {
    const timer1 = setTimeout(() => {
      setAnimationPhase('welcome');
    }, 800);

    const timer2 = setTimeout(() => {
      setAnimationPhase('fadeOut');
    }, 3500);

    const timer3 = setTimeout(() => {
      setAnimationPhase('exit');
    }, 4500);

    const timer4 = setTimeout(() => {
      setIsVisible(false);
    }, 5500);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      clearTimeout(timer4);
    };
  }, []);

  if (!isVisible) return null;

  const getTransformClasses = () => {
    switch (animationPhase) {
      case 'enter':
        return 'transform translate-x-full scale-75 opacity-0';
      case 'welcome':
        return 'transform translate-x-0 scale-100 opacity-100';
      case 'fadeOut':
        return 'transform translate-x-0 scale-100 opacity-60';
      case 'exit':
        return 'transform -translate-x-full scale-75 opacity-0';
      default:
        return 'transform translate-x-0 scale-100 opacity-100';
    }
  };

  const getBackgroundOpacity = () => {
    switch (animationPhase) {
      case 'enter':
        return 'opacity-0';
      case 'welcome':
        return 'opacity-100';
      case 'fadeOut':
        return 'opacity-40';
      case 'exit':
        return 'opacity-0';
      default:
        return 'opacity-100';
    }
  };

  return (
    <div className="fixed inset-0 z-[100] pointer-events-none overflow-hidden">
      {/* Smooth transitioning background overlay */}
      <div className={`absolute inset-0 bg-gradient-to-br from-pink-300/60 via-white/70 to-blue-300/60 backdrop-blur-sm transition-opacity duration-1000 ${getBackgroundOpacity()}`}></div>
      
      {/* Welcome Hello Kitty - Smooth transitions */}
      <div className="absolute top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2">
        <div 
          className={`transition-all duration-1500 ease-in-out ${getTransformClasses()}`}
        >
          <div className="relative">
            {/* Hello Kitty - Mobile optimized sizes */}
            <div className="w-40 h-48 sm:w-56 sm:h-68 md:w-72 md:h-84">
              <HelloKittySticker size="giant" pose="waving" />
            </div>
            
            {/* Welcome Speech Bubble - Smooth fade in/out */}
            {(animationPhase === 'welcome' || animationPhase === 'fadeOut') && (
              <div className={`absolute -top-14 -left-6 sm:-top-18 sm:-left-10 md:-top-22 md:-left-14 animate-gentle-bounce transition-opacity duration-500 ${animationPhase === 'fadeOut' ? 'opacity-60' : 'opacity-100'}`}>
                <div className="relative bg-white border-3 sm:border-4 border-black rounded-xl sm:rounded-2xl px-4 py-3 sm:px-6 sm:py-4 shadow-xl">
                  {/* Inner glow for depth */}
                  <div className="absolute inset-1 bg-gradient-to-br from-pink-50 to-blue-50 rounded-lg opacity-60"></div>
                  
                  <div className="relative z-10">
                    <div className="text-base sm:text-lg md:text-xl font-bold text-pink-600 text-center whitespace-nowrap">
                      Welcome to your notes! 🎀
                    </div>
                    <div className="text-xs sm:text-sm md:text-base text-gray-700 text-center mt-1 font-medium">
                      Batman & Wicky's space ✨
                    </div>
                  </div>
                  
                  {/* Speech bubble tail */}
                  <div className="absolute bottom-[-12px] sm:bottom-[-16px] left-6 sm:left-8 w-0 h-0 border-l-[12px] sm:border-l-[16px] border-l-transparent border-r-[12px] sm:border-r-[16px] border-r-transparent border-t-[12px] sm:border-t-[16px] border-t-black"></div>
                  <div className="absolute bottom-[-8px] sm:bottom-[-12px] left-6 sm:left-8 w-0 h-0 border-l-[12px] sm:border-l-[16px] border-l-transparent border-r-[12px] sm:border-r-[16px] border-r-transparent border-t-[12px] sm:border-t-[16px] border-t-white"></div>
                </div>
              </div>
            )}
            
            {/* Magical sparkles around Kitty - Smooth transitions */}
            <div className={`absolute -top-6 -left-6 sm:-top-8 sm:-left-8 text-yellow-400 text-2xl sm:text-3xl md:text-4xl animate-sparkle transition-opacity duration-500 ${animationPhase === 'fadeOut' ? 'opacity-40' : 'opacity-100'}`}>
              ✨
            </div>
            <div className={`absolute -top-8 right-6 sm:-top-12 sm:right-8 text-pink-400 text-xl sm:text-2xl md:text-3xl animate-sparkle transition-opacity duration-500 ${animationPhase === 'fadeOut' ? 'opacity-40' : 'opacity-100'}`} style={{animationDelay: '0.5s'}}>
              🌟
            </div>
            <div className={`absolute bottom-6 -left-8 sm:bottom-8 sm:-left-12 text-blue-400 text-xl sm:text-2xl md:text-3xl animate-sparkle transition-opacity duration-500 ${animationPhase === 'fadeOut' ? 'opacity-40' : 'opacity-100'}`} style={{animationDelay: '1s'}}>
              💫
            </div>
            <div className={`absolute bottom-8 right-4 sm:bottom-12 sm:right-6 text-yellow-300 text-lg sm:text-xl md:text-2xl animate-sparkle transition-opacity duration-500 ${animationPhase === 'fadeOut' ? 'opacity-40' : 'opacity-100'}`} style={{animationDelay: '1.5s'}}>
              ⭐
            </div>
            
            {/* Floating hearts - Smooth transitions */}
            <div className={`absolute -top-4 left-12 sm:-top-6 sm:left-16 text-pink-500 text-xl sm:text-2xl md:text-3xl animate-float-heart transition-opacity duration-500 ${animationPhase === 'fadeOut' ? 'opacity-40' : 'opacity-100'}`}>
              💖
            </div>
            <div className={`absolute bottom-12 right-8 sm:bottom-16 sm:right-12 text-red-400 text-lg sm:text-xl md:text-2xl animate-float-heart transition-opacity duration-500 ${animationPhase === 'fadeOut' ? 'opacity-40' : 'opacity-100'}`} style={{animationDelay: '0.8s'}}>
              💕
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};