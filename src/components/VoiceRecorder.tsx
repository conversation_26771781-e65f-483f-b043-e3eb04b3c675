import React, { useState, useRef, useEffect } from 'react';
import { Mic, Square, Play, Pause, Trash2, Save, Volume2 } from 'lucide-react';
import * as lamejs from 'lamejs';
import { MediaUtils } from '../utils/mediaUtils';
import type { RecordingState } from '../types/media';

interface VoiceRecorderProps {
  onRecordingComplete: (audioBlob: Blob, duration: number, waveform: number[]) => void;
  onCancel: () => void;
  maxDuration?: number;
  isOpen: boolean;
}

export const VoiceRecorder: React.FC<VoiceRecorderProps> = ({
  onRecordingComplete,
  onCancel,
  maxDuration = 600, // 10 minutes
  isOpen
}) => {
  const [recordingState, setRecordingState] = useState<RecordingState>({
    isRecording: false,
    isPaused: false,
    duration: 0,
    audioLevel: 0
  });
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [waveformData, setWaveformData] = useState<number[]>([]);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const animationRef = useRef<number | null>(null);

  useEffect(() => {
    if (!isOpen) {
      cleanup();
    }
    return cleanup;
  }, [isOpen]);

  const cleanup = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = null;
    }
    
    if (mediaRecorderRef.current && recordingState.isRecording) {
      mediaRecorderRef.current.stop();
    }
    
    if (audioContextRef.current) {
      audioContextRef.current.close();
    }
    
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    
    setRecordingState({
      isRecording: false,
      isPaused: false,
      duration: 0,
      audioLevel: 0
    });
    setAudioBlob(null);
    setAudioUrl(null);
    setIsPlaying(false);
    setWaveformData([]);
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // Set up audio context for level monitoring
      audioContextRef.current = new AudioContext();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      source.connect(analyserRef.current);

      // Set up media recorder
      // We record in raw PCM (wav) and then encode to MP3
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm' // Keep using webm for broad compatibility, will encode later
      });
      
      chunksRef.current = [];
      
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };
      
      mediaRecorderRef.current.onstop = async () => {
        setIsProcessing(true);
        const tempBlob = new Blob(chunksRef.current, { type: 'audio/webm' });

        try {
          const arrayBuffer = await tempBlob.arrayBuffer();
          const audioBuffer = await audioContextRef.current!.decodeAudioData(arrayBuffer);
          const waveform = await MediaUtils.generateWaveform(audioBuffer, 100);
          setWaveformData(waveform);
          
          // Encode to MP3
          const mp3Blob = encodeToMp3(audioBuffer);
          setAudioBlob(mp3Blob);
          setAudioUrl(URL.createObjectURL(mp3Blob));

        } catch (error) {
          console.error('Failed to process audio:', error);
          // Fallback to original blob if encoding fails
          setAudioBlob(tempBlob);
          setAudioUrl(URL.createObjectURL(tempBlob));
          setWaveformData([]);
        } finally {
          setIsProcessing(false);
        }
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current.start(100); // Collect data every 100ms
      
      setRecordingState(prev => ({ ...prev, isRecording: true, duration: 0 }));
      
      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingState(prev => {
          const newDuration = prev.duration + 1;
          if (newDuration >= maxDuration) {
            stopRecording();
            return prev;
          }
          return { ...prev, duration: newDuration };
        });
      }, 1000);
      
      // Start audio level monitoring
      monitorAudioLevel();
      
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Could not access microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && recordingState.isRecording) {
      mediaRecorderRef.current.stop();
    }
    
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = null;
    }
    
    setRecordingState(prev => ({ ...prev, isRecording: false, audioLevel: 0 }));
  };

  const encodeToMp3 = (audioBuffer: AudioBuffer): Blob => {
    const mp3encoder = new lamejs.Mp3Encoder(1, audioBuffer.sampleRate, 128); // 1 channel, sample rate, 128kbps
    const samples = audioBuffer.getChannelData(0);
    const sampleBlockSize = 1152;
    const mp3Data = [];

    const int16Samples = new Int16Array(samples.length);
    for (let i = 0; i < samples.length; i++) {
      int16Samples[i] = samples[i] * 32767.5;
    }

    for (let i = 0; i < int16Samples.length; i += sampleBlockSize) {
      const sampleChunk = int16Samples.subarray(i, i + sampleBlockSize);
      const mp3buf = mp3encoder.encodeBuffer(sampleChunk);
      if (mp3buf.length > 0) {
        mp3Data.push(mp3buf);
      }
    }
    const mp3buf = mp3encoder.flush();

    if (mp3buf.length > 0) {
      mp3Data.push(mp3buf);
    }
    
    return new Blob(mp3Data.map(buf => new Uint8Array(buf)), { type: 'audio/mp3' });
  };

  const monitorAudioLevel = () => {
    if (!analyserRef.current) return;
    
    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    
    const updateLevel = () => {
      if (!analyserRef.current || !recordingState.isRecording) return;
      
      analyserRef.current.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      const normalizedLevel = average / 255;
      
      setRecordingState(prev => ({ ...prev, audioLevel: normalizedLevel }));
      
      animationRef.current = requestAnimationFrame(updateLevel);
    };
    
    updateLevel();
  };

  const playRecording = () => {
    if (!audioUrl) return;
    
    if (!audioRef.current) {
      audioRef.current = new Audio(audioUrl);
      audioRef.current.onended = () => setIsPlaying(false);
    }
    
    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const saveRecording = () => {
    if (audioBlob) {
      onRecordingComplete(audioBlob, recordingState.duration, waveformData);
    }
  };

  const deleteRecording = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
    
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    
    setAudioBlob(null);
    setAudioUrl(null);
    setIsPlaying(false);
    setWaveformData([]);
    setRecordingState(prev => ({ ...prev, duration: 0 }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4 z-[70] safe-area">
      <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl w-full max-w-sm sm:max-w-md border border-pink-100/50">
        {/* Header - Mobile responsive */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-pink-100/50">
          <div className="flex items-center space-x-2">
            <Mic className="w-4 h-4 sm:w-5 sm:h-5 text-pink-400" />
            <h3 className="text-base sm:text-lg font-semibold text-gray-800">Voice Note</h3>
          </div>
          <button
            onClick={onCancel}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200"
          >
            <Square className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
        </div>

        {/* Body - Mobile responsive */}
        <div className="p-4 sm:p-8 flex flex-col items-center justify-center min-h-[250px] sm:min-h-[300px]">
          {isProcessing ? (
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin"></div>
              <p className="text-gray-600">Processing audio...</p>
            </div>
          ) : audioUrl ? (
            <div className="w-full flex flex-col items-center justify-center space-y-4 sm:space-y-6">
              <div className="flex items-center justify-center space-x-4">
                <button
                  onClick={playRecording}
                  className="w-16 h-16 sm:w-20 sm:h-20 bg-pink-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-pink-600 transition-all"
                >
                  {isPlaying ? <Pause className="w-8 h-8 sm:w-10 sm:h-10" /> : <Play className="w-8 h-8 sm:w-10 sm:h-10" />}
                </button>
              </div>
              <div className="text-center">
                <p className="text-lg sm:text-xl font-semibold text-gray-800">{MediaUtils.formatDuration(recordingState.duration)}</p>
                <p className="text-sm text-gray-500">{audioBlob ? MediaUtils.formatFileSize(audioBlob.size) : ''}</p>
              </div>
            </div>
          ) : (
            <div className="w-full flex flex-col items-center justify-center space-y-4 sm:space-y-6">
              {/* Recording button */}
              <button
                onClick={recordingState.isRecording ? stopRecording : startRecording}
                className="w-20 h-20 sm:w-24 sm:h-24 bg-red-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-red-600 transition-all relative"
              >
                {recordingState.isRecording && (
                  <div 
                    className="absolute inset-0 rounded-full bg-red-400/50 transition-transform duration-200" 
                    style={{ transform: `scale(${1 + recordingState.audioLevel * 0.2})` }}
                  ></div>
                )}
                {recordingState.isRecording ? <Square className="w-8 h-8 sm:w-10 sm:h-10" /> : <Mic className="w-8 h-8 sm:w-10 sm:h-10" />}
              </button>
              
              {/* Duration display */}
              <p className="text-lg sm:text-xl font-semibold text-gray-800">
                {MediaUtils.formatDuration(recordingState.duration)}
              </p>
              <p className="text-xs text-gray-500 sm:text-sm">
                Max duration: {MediaUtils.formatDuration(maxDuration)}
              </p>
            </div>
          )}
        </div>

        {/* Footer - Mobile responsive */}
        <div className="p-4 sm:p-6 bg-gray-50/50 rounded-b-2xl flex items-center justify-between">
          <button
            onClick={deleteRecording}
            disabled={!audioBlob || isProcessing}
            className="flex items-center space-x-2 px-3 py-2 sm:px-4 sm:py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Trash2 className="w-4 h-4" />
            <span className="hidden sm:inline">Delete</span>
          </button>
          
          <button
            onClick={saveRecording}
            disabled={!audioBlob || isProcessing}
            className="flex items-center space-x-2 px-3 py-2 sm:px-4 sm:py-2 bg-gradient-to-r from-pink-400 to-pink-500 text-white rounded-lg hover:from-pink-500 hover:to-pink-600 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? (
              <>
                <div className="w-4 h-4 border-2 border-white/50 border-t-white rounded-full animate-spin"></div>
                <span>Processing...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span className="hidden sm:inline">Save Note</span>
                <span className="sm:hidden">Save</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};