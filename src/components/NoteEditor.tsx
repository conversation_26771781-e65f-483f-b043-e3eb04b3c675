import React, { useState, useEffect } from 'react';
import { Save, X, Sparkles } from 'lucide-react';
import { InlineMediaEditor } from './InlineMediaEditor';
import type { InlineMediaElement } from '../types/media';

interface Note {
  id: string;
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
}

interface NoteEditorProps {
  note: Note | null;
  onSave: (note: Omit<Note, 'id'>) => void;
  onClose: () => void;
  isOpen: boolean;
}

export const NoteEditor: React.FC<NoteEditorProps> = ({ note, onSave, onClose, isOpen }) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [mediaElements, setMediaElements] = useState<InlineMediaElement[]>([]);

  useEffect(() => {
    if (note) {
      setTitle(note.title);
      setContent(note.content);
    } else {
      setTitle('');
      setContent('');
      setMediaElements([]);
    }
  }, [note]);

  const handleContentChange = (newContent: string, newMediaElements: InlineMediaElement[]) => {
    setContent(newContent);
    setMediaElements(newMediaElements);
  };

  const handleSave = () => {
    if (!title.trim() && !content.trim()) return;
    
    onSave({
      title: title.trim(),
      content: content.trim(),
      createdAt: note?.createdAt || new Date(),
      updatedAt: new Date()
    });
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 's' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center p-4 z-[60]">
      <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden border border-pink-100/50">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-pink-100/50 bg-gradient-to-r from-pink-50/50 to-blue-50/50">
          <div className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5 text-pink-400" />
            <h2 className="text-lg font-semibold text-gray-800">
              {note ? 'Edit Note' : 'New Note'}
            </h2>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleSave}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-pink-400 to-pink-500 text-white rounded-lg hover:from-pink-500 hover:to-pink-600 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <Save className="w-4 h-4" />
              <span>Save</span>
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        {/* Content */}
        <div className="p-6 max-h-[calc(90vh-140px)] overflow-y-auto" onKeyDown={handleKeyDown}>
          <input
            type="text"
            placeholder="Note title..."
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full text-xl font-semibold text-gray-800 bg-transparent border-none outline-none placeholder-gray-400 mb-4"
            autoFocus
          />
          
          <div className="w-full h-px bg-gradient-to-r from-pink-200 via-pink-300 to-pink-200 opacity-50 mb-6"></div>
          
          <InlineMediaEditor
            noteId={note?.id || null}
            content={content}
            onContentChange={handleContentChange}
            placeholder="Start writing your note... Drop photos, videos, or audio files anywhere!"
            className="min-h-[300px]"
          />
        </div>
        
        {/* Decorative elements */}
        <div className="absolute top-4 right-20 w-4 h-4 bg-gradient-to-br from-pink-300 to-pink-400 rounded-full opacity-40 transform rotate-45"></div>
        <div className="absolute top-6 right-22 w-2 h-2 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full opacity-40 transform rotate-45"></div>
      </div>
    </div>
  );
};