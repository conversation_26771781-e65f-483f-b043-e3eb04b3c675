import React from 'react';
import { Edit3, Trash2, Calendar } from 'lucide-react';

interface Note {
  id: string;
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
}

interface NoteCardProps {
  note: Note;
  onEdit: (note: Note) => void;
  onDelete: (id: string) => void;
}

export const NoteCard: React.FC<NoteCardProps> = ({ note, onEdit, onDelete }) => {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="group relative bg-white/80 backdrop-blur-sm rounded-xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-pink-100/50">
      {/* Kitty bow decoration */}
      <div className="absolute -top-1.5 -right-1.5 sm:-top-2 sm:-right-2 w-4 h-4 sm:w-6 sm:h-6 bg-gradient-to-br from-pink-300 to-pink-400 rounded-full opacity-60 transform rotate-45"></div>
      <div className="absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-br from-pink-400 to-pink-500 rounded-full opacity-80 transform rotate-45"></div>
      
      {/* Note content */}
      <div className="mb-3 sm:mb-4">
        <h3 className="font-semibold text-gray-800 text-base sm:text-lg mb-2 line-clamp-1">
          {note.title || 'Untitled Note'}
        </h3>
        <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed">
          {note.content || 'Empty note...'}
        </p>
      </div>
      
      {/* Date and actions */}
      <div className="flex items-center justify-between pt-3 border-t border-pink-100/50">
        <div className="flex items-center text-xs text-gray-500">
          <Calendar className="w-3 h-3 mr-1" />
          <span className="hidden sm:inline">{formatDate(note.updatedAt)}</span>
          <span className="sm:hidden">{new Intl.DateTimeFormat('en-US', { month: 'short', day: 'numeric' }).format(note.updatedAt)}</span>
        </div>
        
        <div className="flex items-center space-x-1 sm:space-x-2 opacity-100 sm:opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            onClick={() => onEdit(note)}
            className="p-1.5 sm:p-2 text-blue-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
          >
            <Edit3 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </button>
          <button
            onClick={() => onDelete(note.id)}
            className="p-1.5 sm:p-2 text-pink-400 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-all duration-200"
          >
            <Trash2 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </button>
        </div>
      </div>
      
      {/* Whisker decoration */}
      <div className="absolute bottom-2 left-3 sm:left-4 w-6 sm:w-8 h-px bg-gradient-to-r from-pink-200 to-transparent opacity-30"></div>
      <div className="absolute bottom-3 sm:bottom-4 left-3 sm:left-4 w-4 sm:w-6 h-px bg-gradient-to-r from-pink-200 to-transparent opacity-30"></div>
    </div>
  );
};