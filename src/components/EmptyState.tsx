import React from 'react';
import { FileTex<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';

interface EmptyStateProps {
  onNewNote: () => void;
  isSearch?: boolean;
  searchTerm?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ onNewNote, isSearch, searchTerm }) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 sm:py-16 px-4">
      <div className="relative mb-6 sm:mb-8">
        {/* Note icon */}
        <div className="w-24 h-24 sm:w-32 sm:h-32 bg-gradient-to-br from-pink-100 to-blue-100 rounded-full flex items-center justify-center opacity-60">
          {isSearch ? (
            <Heart className="w-12 h-12 sm:w-16 sm:h-16 text-pink-300 fill-current" />
          ) : (
            <FileText className="w-12 h-12 sm:w-16 sm:h-16 text-pink-300 fill-current" />
          )}
        </div>
        
        {/* Decorative elements */}
        <div className="absolute -top-1.5 -right-1.5 sm:-top-2 sm:-right-2 w-4 h-4 sm:w-6 sm:h-6 bg-gradient-to-br from-pink-300 to-pink-400 rounded-full opacity-60 transform rotate-45"></div>
        <div className="absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-br from-pink-400 to-pink-500 rounded-full opacity-80 transform rotate-45"></div>
        <Sparkles className="absolute -bottom-1.5 -left-1.5 sm:-bottom-2 sm:-left-2 w-4 h-4 sm:w-6 sm:h-6 text-blue-300 opacity-50" />
      </div>
      
      <div className="text-center space-y-3 sm:space-y-4 max-w-md">
        {isSearch ? (
          <>
            <h3 className="text-lg sm:text-xl font-semibold text-gray-600">
              No notes found for "{searchTerm}"
            </h3>
            <p className="text-gray-500 text-sm leading-relaxed px-4">
              Try searching with different keywords or create a new note about this topic!
            </p>
          </>
        ) : (
          <>
            <h3 className="text-lg sm:text-xl font-semibold text-gray-600">
              No notes yet! ✨
            </h3>
            <p className="text-gray-500 text-sm leading-relaxed px-4">
              Start by creating your first note. Both Batman and Wicky can see and create notes here.
            </p>
          </>
        )}
        
        <button
          onClick={onNewNote}
          className="inline-flex items-center space-x-2 px-5 sm:px-6 py-2.5 sm:py-3 bg-gradient-to-r from-pink-400 to-pink-500 text-white rounded-xl hover:from-pink-500 hover:to-pink-600 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 font-medium text-sm sm:text-base"
        >
          <FileText className="w-4 h-4" />
          <span>{isSearch ? 'Create Note' : 'Create Your First Note'}</span>
        </button>
      </div>
      
      {/* Bottom decoration */}
      <div className="mt-8 sm:mt-12 flex space-x-2 opacity-30">
        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pink-300 rounded-full animate-pulse"></div>
        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-300 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pink-300 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
      </div>
      
      {!isSearch && (
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-400">
            💖 A shared space for Batman and Wicky 💖
          </p>
        </div>
      )}
    </div>
  );
};