import React, { useRef, useState } from 'react';
import { Camera, Image, X, Upload } from 'lucide-react';

interface CameraCaptureProps {
  onImageSelect: (files: File[]) => void;
  disabled?: boolean;
  maxImages?: number;
}

export const CameraCapture: React.FC<CameraCaptureProps> = ({
  onImageSelect,
  disabled = false,
  maxImages = 5
}) => {
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const galleryInputRef = useRef<HTMLInputElement>(null);
  const [showOptions, setShowOptions] = useState(false);

  // Detect platform for better UX
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
  const isAndroid = /Android/i.test(navigator.userAgent);

  const handleCameraClick = () => {
    if (disabled) return;
    
    // On mobile, show options. On desktop, go straight to file picker
    if (isMobile) {
      setShowOptions(true);
    } else {
      // Desktop - open file picker with camera preference
      galleryInputRef.current?.click();
    }
  };

  const handleTakePhoto = () => {
    setShowOptions(false);
    cameraInputRef.current?.click();
  };

  const handleChooseFromGallery = () => {
    setShowOptions(false);
    galleryInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      onImageSelect(files.slice(0, maxImages));
    }
    // Reset input
    if (e.target) e.target.value = '';
  };

  const closeOptions = () => {
    setShowOptions(false);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      closeOptions();
    }
  };

  return (
    <>
      {/* Main Camera Button */}
      <button
        type="button"
        onClick={handleCameraClick}
        disabled={disabled}
        className="flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 rounded-lg hover:from-blue-200 hover:to-blue-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed border border-blue-200 shadow-sm"
      >
        <Camera className="w-4 h-4" />
        <span className="text-sm font-medium">
          {isMobile ? 'Camera' : 'Photos'}
        </span>
      </button>

      {/* Mobile Options Bottom Sheet - Fixed positioning and better visibility */}
      {showOptions && (
        <div 
          className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-end justify-center z-[90] safe-area"
          onClick={handleBackdropClick}
          style={{ 
            paddingBottom: 'env(safe-area-inset-bottom)',
            paddingLeft: 'env(safe-area-inset-left)',
            paddingRight: 'env(safe-area-inset-right)'
          }}
        >
          <div className="bg-white rounded-t-3xl shadow-2xl w-full max-w-md mx-4 mb-0 border-t border-gray-200 animate-in slide-in-from-bottom duration-300 overflow-hidden">
            {/* Header with better spacing */}
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50/50 to-pink-50/50">
              <h3 className="text-lg sm:text-xl font-semibold text-gray-800">Add Photo</h3>
              <button
                onClick={closeOptions}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-all duration-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Options with better mobile spacing */}
            <div className="p-4 sm:p-6 space-y-4">
              {/* Take Photo Option */}
              <button
                onClick={handleTakePhoto}
                className="w-full flex items-center space-x-4 p-4 sm:p-5 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 rounded-2xl transition-all duration-200 border border-blue-200 shadow-sm active:scale-95"
              >
                <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                  <Camera className="w-7 h-7 text-white" />
                </div>
                <div className="text-left flex-1">
                  <div className="font-semibold text-gray-800 text-base sm:text-lg">Take Photo</div>
                  <div className="text-sm text-gray-600 mt-1">
                    {isIOS ? 'Use camera to take a new photo' : 'Capture with camera'}
                  </div>
                </div>
              </button>

              {/* Choose from Gallery Option */}
              <button
                onClick={handleChooseFromGallery}
                className="w-full flex items-center space-x-4 p-4 sm:p-5 bg-gradient-to-r from-pink-50 to-pink-100 hover:from-pink-100 hover:to-pink-200 rounded-2xl transition-all duration-200 border border-pink-200 shadow-sm active:scale-95"
              >
                <div className="w-14 h-14 bg-gradient-to-br from-pink-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg">
                  <Image className="w-7 h-7 text-white" />
                </div>
                <div className="text-left flex-1">
                  <div className="font-semibold text-gray-800 text-base sm:text-lg">
                    {isIOS ? 'Photo Library' : isAndroid ? 'Gallery' : 'Choose Photo'}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    Select from existing photos
                  </div>
                </div>
              </button>

              {/* File Upload Option (for desktop-like experience) */}
              {!isMobile && (
                <button
                  onClick={handleChooseFromGallery}
                  className="w-full flex items-center space-x-4 p-4 sm:p-5 bg-gradient-to-r from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 rounded-2xl transition-all duration-200 border border-green-200 shadow-sm active:scale-95"
                >
                  <div className="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                    <Upload className="w-7 h-7 text-white" />
                  </div>
                  <div className="text-left flex-1">
                    <div className="font-semibold text-gray-800 text-base sm:text-lg">Browse Files</div>
                    <div className="text-sm text-gray-600 mt-1">
                      Select from computer
                    </div>
                  </div>
                </button>
              )}
            </div>

            {/* Platform-specific tips with better mobile formatting */}
            <div className="px-4 sm:px-6 pb-6">
              <div className="bg-gradient-to-r from-gray-50 to-blue-50/30 rounded-xl p-4 border border-gray-100">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span className="text-xs font-medium text-gray-700 uppercase tracking-wide">Tip</span>
                </div>
                <p className="text-sm text-gray-600 leading-relaxed">
                  {isIOS && 'Allow camera access in Settings > Safari > Camera for the best experience'}
                  {isAndroid && 'Grant camera and storage permissions for full functionality'}
                  {!isMobile && 'You can also drag and drop images anywhere on the page'}
                </p>
              </div>
            </div>

            {/* Safe area padding for iOS */}
            <div className="h-safe-bottom"></div>
          </div>
        </div>
      )}

      {/* Hidden File Inputs */}
      {/* Camera Input - optimized for camera capture */}
      <input
        ref={cameraInputRef}
        type="file"
        accept="image/*"
        multiple={maxImages > 1}
        onChange={handleFileChange}
        className="hidden"
        capture="environment" // Prefer rear camera - this is the standard way
      />
      
      {/* Gallery Input - for selecting existing photos */}
      <input
        ref={galleryInputRef}
        type="file"
        accept="image/*"
        multiple={maxImages > 1}
        onChange={handleFileChange}
        className="hidden"
        // No capture attribute - this allows gallery selection
      />
    </>
  );
};