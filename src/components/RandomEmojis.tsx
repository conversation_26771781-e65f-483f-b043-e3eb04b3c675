import React, { useState, useEffect } from 'react';

interface EmojiParticle {
  id: string;
  emoji: string;
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
}

export const RandomEmojis: React.FC = () => {
  const [emojis, setEmojis] = useState<EmojiParticle[]>([]);

  const kawaiEmojis = ['🎀', '💖', '✨', '🌸', '🌟', '💕', '🦄', '🍓', '🌈', '💫', '⭐', '🎈', '🧸', '🍰', '🌺', '🦋'];

  const createEmoji = (): EmojiParticle => {
    const side = Math.floor(Math.random() * 4); // 0: top, 1: right, 2: bottom, 3: left
    let x, y, vx, vy;

    switch (side) {
      case 0: // top
        x = Math.random() * window.innerWidth;
        y = -50;
        vx = (Math.random() - 0.5) * 2;
        vy = Math.random() * 2 + 1;
        break;
      case 1: // right
        x = window.innerWidth + 50;
        y = Math.random() * window.innerHeight;
        vx = -(Math.random() * 2 + 1);
        vy = (Math.random() - 0.5) * 2;
        break;
      case 2: // bottom
        x = Math.random() * window.innerWidth;
        y = window.innerHeight + 50;
        vx = (Math.random() - 0.5) * 2;
        vy = -(Math.random() * 2 + 1);
        break;
      default: // left
        x = -50;
        y = Math.random() * window.innerHeight;
        vx = Math.random() * 2 + 1;
        vy = (Math.random() - 0.5) * 2;
        break;
    }

    const maxLife = 3000 + Math.random() * 2000; // 3-5 seconds

    return {
      id: Math.random().toString(36).substr(2, 9),
      emoji: kawaiEmojis[Math.floor(Math.random() * kawaiEmojis.length)],
      x,
      y,
      vx,
      vy,
      life: maxLife,
      maxLife
    };
  };

  useEffect(() => {
    const spawnInterval = setInterval(() => {
      if (Math.random() < 0.3) { // 30% chance every interval
        setEmojis(prev => [...prev, createEmoji()]);
      }
    }, 2000);

    const animationFrame = setInterval(() => {
      setEmojis(prev => prev
        .map(emoji => ({
          ...emoji,
          x: emoji.x + emoji.vx,
          y: emoji.y + emoji.vy,
          life: emoji.life - 16 // ~60fps
        }))
        .filter(emoji => 
          emoji.life > 0 && 
          emoji.x > -100 && 
          emoji.x < window.innerWidth + 100 && 
          emoji.y > -100 && 
          emoji.y < window.innerHeight + 100
        )
      );
    }, 16);

    return () => {
      clearInterval(spawnInterval);
      clearInterval(animationFrame);
    };
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none z-[3] overflow-hidden">
      {emojis.map(emoji => {
        const opacity = Math.min(1, emoji.life / (emoji.maxLife * 0.3));
        const scale = 0.5 + (emoji.life / emoji.maxLife) * 0.5;
        
        return (
          <div
            key={emoji.id}
            className="absolute text-2xl sm:text-3xl transition-all duration-75 ease-linear"
            style={{
              left: emoji.x,
              top: emoji.y,
              opacity,
              transform: `scale(${scale}) rotate(${(emoji.maxLife - emoji.life) * 0.1}deg)`,
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
            }}
          >
            {emoji.emoji}
          </div>
        );
      })}
    </div>
  );
};