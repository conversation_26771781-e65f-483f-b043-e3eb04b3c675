import React, { useState, useEffect } from 'react';
import { Edit3, Trash2, Calendar, User } from 'lucide-react';
import { useModal } from '../hooks/useModal';
import { Modal } from './Modal';
import type { CommunityNote } from '../lib/supabase';

interface CommunityNoteCardProps {
  note: CommunityNote;
  onEdit: (note: CommunityNote) => void;
  onDelete: (id: string) => void;
}

export const CommunityNoteCard: React.FC<CommunityNoteCardProps> = ({ 
  note, 
  onEdit, 
  onDelete
}) => {
  const { modalState, hideModal, showDeleteConfirm } = useModal();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    const noteTitle = note.title || 'Untitled Note';
    showDeleteConfirm(noteTitle, () => {
      onDelete(note.id);
      hideModal();
    });
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit(note);
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Prevent event bubbling from buttons
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }
    onEdit(note);
  };

  // Determine colors based on author
  const isWicky = note.author_name.toLowerCase() === 'wicky';
  const isBatman = note.author_name.toLowerCase() === 'batman';
  
  const getThemeColors = () => {
    if (isWicky) {
      return {
        background: 'bg-pink-50/80',
        border: 'border-pink-200/60',
        bowPrimary: 'from-pink-300 to-pink-400',
        bowSecondary: 'from-pink-400 to-pink-500',
        whiskers: 'from-pink-200',
        borderAccent: 'border-pink-100/50',
        authorColor: '#ec4899'
      };
    } else if (isBatman) {
      return {
        background: 'bg-blue-50/80',
        border: 'border-blue-200/60',
        bowPrimary: 'from-blue-300 to-blue-400',
        bowSecondary: 'from-blue-400 to-blue-500',
        whiskers: 'from-blue-200',
        borderAccent: 'border-blue-100/50',
        authorColor: '#3b82f6'
      };
    } else {
      return {
        background: 'bg-white/80',
        border: 'border-pink-100/50',
        bowPrimary: 'from-pink-300 to-pink-400',
        bowSecondary: 'from-pink-400 to-pink-500',
        whiskers: 'from-pink-200',
        borderAccent: 'border-pink-100/50',
        authorColor: note.author_color
      };
    }
  };

  const colors = getThemeColors();

  return (
    <>
      <div 
        className={`group relative ${colors.background} backdrop-blur-sm rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border ${colors.border} cursor-pointer h-full`}
        onClick={handleCardClick}
      >
        {/* Optimized layout with better space distribution */}
        <div className="flex flex-col h-full p-4 sm:p-6 min-h-[200px] sm:min-h-[220px]">
          
          {/* Header Section - Compact */}
          <div className="flex items-center justify-between mb-3 flex-shrink-0">
            <div className="flex items-center space-x-2 min-w-0">
              <User 
                className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" 
                style={{ color: colors.authorColor }}
              />
              <span className="text-sm font-medium text-gray-700 truncate">{note.author_name}</span>
            </div>
          </div>

          {/* Content Section - Flexible with proper spacing */}
          <div className="flex-1 flex flex-col justify-between min-h-0">
            {/* Title and content */}
            <div className="mb-4 flex-1 min-h-0">
              <h3 className="font-semibold text-gray-800 text-base sm:text-lg mb-2 line-clamp-2 break-words">
                {note.title || 'Untitled Note'}
              </h3>
              <p className="text-gray-600 text-sm line-clamp-3 sm:line-clamp-4 leading-relaxed break-words">
                {note.content || 'Empty note...'}
              </p>
            </div>
            
            {/* Footer Section - Always at bottom */}
            <div className={`flex items-center justify-between pt-3 border-t ${colors.borderAccent} flex-shrink-0`}>
              <div className="flex items-center text-xs text-gray-500 min-w-0">
                <Calendar className="w-3 h-3 mr-1 flex-shrink-0" />
                <span className="hidden sm:inline truncate">{formatDate(note.updated_at)}</span>
                <span className="sm:hidden truncate">{new Intl.DateTimeFormat('en-US', { month: 'short', day: 'numeric' }).format(new Date(note.updated_at))}</span>
              </div>
              
              <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                {/* Action buttons - visible on hover for desktop, always visible on mobile */}
                <div className="flex items-center space-x-1 opacity-100 sm:opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button
                    onClick={handleEdit}
                    className="p-1.5 sm:p-2 text-blue-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                    title="Edit note"
                  >
                    <Edit3 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                  </button>
                  <button
                    onClick={handleDelete}
                    className="p-1.5 sm:p-2 text-pink-400 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-all duration-200"
                    title="Delete note"
                  >
                    <Trash2 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Decorative elements */}
        <div className={`absolute -top-1.5 -right-1.5 sm:-top-2 sm:-right-2 w-4 h-4 sm:w-6 sm:h-6 bg-gradient-to-br ${colors.bowPrimary} rounded-full opacity-60 transform rotate-45`}></div>
        <div className={`absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-br ${colors.bowSecondary} rounded-full opacity-80 transform rotate-45`}></div>
        
        {/* Whisker decoration */}
        <div className={`absolute bottom-2 left-3 sm:left-4 w-6 sm:w-8 h-px bg-gradient-to-r ${colors.whiskers} to-transparent opacity-30`}></div>
        <div className={`absolute bottom-3 sm:bottom-4 left-3 sm:left-4 w-4 sm:w-6 h-px bg-gradient-to-r ${colors.whiskers} to-transparent opacity-30`}></div>
      </div>

      {/* Beautiful Modal */}
      <Modal
        isOpen={modalState.isOpen}
        onClose={hideModal}
        onConfirm={modalState.onConfirm}
        title={modalState.title}
        message={modalState.message}
        type={modalState.type}
        confirmText={modalState.confirmText}
        cancelText={modalState.cancelText}
        showCancel={modalState.showCancel}
      />
    </>
  );
};