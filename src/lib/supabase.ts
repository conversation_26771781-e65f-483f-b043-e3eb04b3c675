import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

let supabase: any;

// Check if environment variables are properly set (not placeholder values)
if (!supabaseUrl || !supabaseAnonKey || 
    supabaseUrl === 'your_supabase_project_url_here' || 
    supabaseAnonKey === 'your_supabase_anon_key_here') {
  console.warn('Supabase environment variables are not configured. Using mock client.');
  
  // Create a comprehensive mock client that won't cause errors
  const mockQueryBuilder = {
    select: () => mockQueryBuilder,
    insert: () => mockQueryBuilder,
    update: () => mockQueryBuilder,
    delete: () => mockQueryBuilder,
    eq: () => mockQueryBuilder,
    order: () => mockQueryBuilder,
    single: () => Promise.resolve({ data: null, error: null }),
    then: (resolve: any) => resolve({ data: [], error: null })
  };

  const mockChannel = {
    on: () => mockChannel,
    subscribe: () => mockChannel
  };

  // Enhanced mock storage with better error handling
  const mockStorage = {
    from: (bucket: string) => ({
      upload: async (path: string, file: File | Blob) => {
        console.log(`Mock upload to ${bucket}/${path}:`, file);
        
        // Simulate realistic upload delay
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
        
        // Create a mock blob URL for the uploaded file
        const mockUrl = URL.createObjectURL(file);
        
        return { 
          data: { 
            path: `${bucket}/${path}`,
            id: Math.random().toString(36).substr(2, 9),
            fullPath: `${bucket}/${path}`,
            Key: `${bucket}/${path}`
          }, 
          error: null 
        };
      },
      remove: async (paths: string[]) => {
        console.log(`Mock remove from ${bucket}:`, paths);
        await new Promise(resolve => setTimeout(resolve, 200));
        return { data: null, error: null };
      },
      getPublicUrl: (path: string) => {
        // Create a mock URL that looks realistic
        const mockUrl = `https://mock-storage.supabase.co/storage/v1/object/public/${bucket}/${path}?t=${Date.now()}`;
        console.log(`Mock public URL for ${bucket}/${path}:`, mockUrl);
        return { 
          data: { 
            publicUrl: mockUrl
          } 
        };
      }
    })
  };

  supabase = {
    from: () => mockQueryBuilder,
    rpc: () => Promise.resolve({ data: null, error: null }),
    channel: () => mockChannel,
    removeChannel: () => {},
    storage: mockStorage,
    auth: {
      signUp: () => Promise.resolve({ data: null, error: null }),
      signIn: () => Promise.resolve({ data: null, error: null }),
      signOut: () => Promise.resolve({ error: null }),
      getUser: () => Promise.resolve({ data: { user: null }, error: null }),
    },
  };
} else {
  supabase = createClient(supabaseUrl, supabaseAnonKey);
}

export { supabase };

export type CommunityNote = {
  id: string;
  title: string;
  content: string;
  author_name: string;
  author_color: string;
  created_at: string;
  updated_at: string;
};