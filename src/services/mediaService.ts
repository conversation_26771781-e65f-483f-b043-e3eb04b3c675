import { supabase } from '../lib/supabase';
import { MediaUtils } from '../utils/mediaUtils';
import type { NoteAttachment, MediaUploadProgress } from '../types/media';

export class MediaService {
  private static readonly STORAGE_BUCKETS = {
    images: 'note_images',
    voice: 'note_voice',
    videos: 'note_videos'
  };

  // Get public URL for an attachment
  static getMediaUrl(attachment: NoteAttachment): string {
    const bucket = this.getBucketForType(attachment.type);
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(attachment.storage_path);
    return data.publicUrl;
  }

  // Legacy method aliases for backward compatibility
  static getImageUrl(attachment: NoteAttachment): string {
    return this.getMediaUrl(attachment);
  }

  static getVoiceUrl(attachment: NoteAttachment): string {
    return this.getMediaUrl(attachment);
  }

  static getVideoUrl(attachment: NoteAttachment): string {
    return this.getMediaUrl(attachment);
  }

  private static getBucketForType(type: string): string {
    switch (type) {
      case 'image': return this.STORAGE_BUCKETS.images;
      case 'voice': return this.STORAGE_BUCKETS.voice;
      case 'video': return this.STORAGE_BUCKETS.videos;
      default: return this.STORAGE_BUCKETS.images;
    }
  }

  // Upload any media file with unified handling
  static async uploadMedia(
    file: File,
    noteId: string,
    type: 'image' | 'voice' | 'video',
    onProgress?: (progress: MediaUploadProgress) => void,
    waveformData?: number[]
  ): Promise<NoteAttachment> {
    const uploadId = Math.random().toString(36).substr(2, 9);
    
    try {
      onProgress?.({ id: uploadId, progress: 10, status: 'uploading' });

      // Validate file
      const validation = this.validateFile(file, type);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      onProgress?.({ id: uploadId, progress: 30, status: 'processing' });

      // Process file based on type
      const metadata = await this.processFile(file, type, waveformData);
      
      onProgress?.({ id: uploadId, progress: 60, status: 'uploading' });

      // Upload to storage
      const bucket = this.getBucketForType(type);
      const filePath = `${noteId}/${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
      
      const { data: uploadResult, error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw new Error(uploadError.message || 'Failed to upload file');
      }

      onProgress?.({ id: uploadId, progress: 90, status: 'uploading' });

      // Save to database
      const attachmentData = {
        note_id: noteId,
        type,
        file_name: file.name,
        file_size: file.size,
        mime_type: file.type,
        storage_path: filePath,
        duration_seconds: metadata.duration || 0,
        metadata: metadata.metadata || {}
      };

      const { data: attachment, error: dbError } = await supabase
        .from('note_attachments')
        .insert(attachmentData)
        .select()
        .single();

      if (dbError) {
        // Clean up uploaded file if DB save fails
        await supabase.storage.from(bucket).remove([filePath]);
        throw new Error('Failed to save attachment to database');
      }

      onProgress?.({ id: uploadId, progress: 100, status: 'complete' });
      return attachment;
    } catch (error) {
      console.error('Media upload error:', error);
      onProgress?.({ 
        id: uploadId, 
        progress: 0, 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Upload failed' 
      });
      throw error;
    }
  }

  private static validateFile(file: File, type: 'image' | 'voice' | 'video'): { valid: boolean; error?: string } {
    switch (type) {
      case 'image':
        return MediaUtils.validateImageFile(file);
      case 'voice':
        return MediaUtils.validateVoiceFile(file);
      case 'video':
        return MediaUtils.validateVideoFile(file);
      default:
        return { valid: false, error: 'Unsupported file type' };
    }
  }

  private static async processFile(
    file: File, 
    type: 'image' | 'voice' | 'video', 
    waveformData?: number[]
  ): Promise<{ duration?: number; metadata: any }> {
    switch (type) {
      case 'image':
        return { metadata: await this.processImageFile(file) };
      case 'voice':
        return { 
          duration: await MediaUtils.getAudioDuration(file),
          metadata: { 
            waveform: waveformData || await this.generateWaveform(file),
            sample_rate: 44100 
          }
        };
             case 'video':
         const videoInfo = await MediaUtils.getVideoInfo(file);
         return { 
           duration: videoInfo.duration,
           metadata: {
             width: videoInfo.width,
             height: videoInfo.height,
             aspectRatio: videoInfo.width / videoInfo.height
           }
         };
      default:
        return { metadata: {} };
    }
  }

  private static async processImageFile(file: File): Promise<any> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
          aspectRatio: img.width / img.height,
          naturalWidth: img.naturalWidth,
          naturalHeight: img.naturalHeight
        });
      };
      img.onerror = () => resolve({});
      img.src = URL.createObjectURL(file);
    });
  }



  private static async generateWaveform(file: File): Promise<number[]> {
    try {
      const audioContext = new AudioContext();
      const arrayBuffer = await file.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      const waveform = await MediaUtils.generateWaveform(audioBuffer);
      audioContext.close();
      return waveform;
    } catch (error) {
      console.warn('Failed to generate waveform:', error);
      return [];
    }
  }

  // Get all attachments for a note
  static async getAttachments(noteId: string): Promise<NoteAttachment[]> {
    try {
      const { data, error } = await supabase
        .from('note_attachments')
        .select('*')
        .eq('note_id', noteId)
        .order('created_at', { ascending: true });

      if (error) {
        console.warn('Failed to load attachments:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.warn('Error loading attachments:', error);
      return [];
    }
  }

  // Delete an attachment
  static async deleteAttachment(attachmentId: string): Promise<void> {
    try {
      // Get attachment info first
      const { data: attachment, error: fetchError } = await supabase
        .from('note_attachments')
        .select('*')
        .eq('id', attachmentId)
        .single();

      if (fetchError || !attachment) {
        throw new Error('Attachment not found');
      }

      // Delete from storage
      const bucket = this.getBucketForType(attachment.type);
      const { error: storageError } = await supabase.storage
        .from(bucket)
        .remove([attachment.storage_path]);

      if (storageError) {
        console.warn('Failed to delete from storage:', storageError);
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('note_attachments')
        .delete()
        .eq('id', attachmentId);

      if (dbError) {
        throw new Error('Failed to delete attachment from database');
      }
    } catch (error) {
      console.error('Error deleting attachment:', error);
      throw error;
    }
  }

  // Legacy method aliases for backward compatibility
  static async uploadImage(file: File, noteId: string, onProgress?: (progress: MediaUploadProgress) => void): Promise<NoteAttachment> {
    return this.uploadMedia(file, noteId, 'image', onProgress);
  }

  static async uploadVoiceNote(file: File, noteId: string, waveformData?: number[], onProgress?: (progress: MediaUploadProgress) => void): Promise<NoteAttachment> {
    return this.uploadMedia(file, noteId, 'voice', onProgress, waveformData);
  }

  static async uploadVideo(file: File, noteId: string, onProgress?: (progress: MediaUploadProgress) => void): Promise<NoteAttachment> {
    return this.uploadMedia(file, noteId, 'video', onProgress);
  }
}