export interface NoteAttachment {
  id: string;
  note_id: string;
  type: 'image' | 'voice' | 'video';
  file_name: string;
  file_size: number;
  mime_type: string;
  storage_path: string;
  thumbnail_path?: string;
  duration_seconds?: number;
  metadata: {
    width?: number;
    height?: number;
    waveform?: number[];
    [key: string]: any;
  };
  created_at: string;
}

export interface MediaUploadProgress {
  id: string;
  progress: number;
  status: 'uploading' | 'processing' | 'complete' | 'error';
  error?: string;
}

export interface RecordingState {
  isRecording: boolean;
  duration: number;
  level: number;
}

// New inline media types for the rebuilt system
export interface InlineMediaElement {
  id: string;
  type: 'image' | 'voice' | 'video';
  attachment: NoteAttachment;
  position: number; // Character position in content
  size?: 'small' | 'medium' | 'large'; // For images
}

export interface MediaBlock {
  id: string;
  type: 'text' | 'media';
  content?: string; // For text blocks
  mediaElement?: InlineMediaElement; // For media blocks
  order: number;
}