import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';

// Enhanced PWA Service Worker Registration
const registerServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      });

      console.log('✅ SW registered successfully:', registration);

      // Check for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content is available
              console.log('🔄 New version available');
              
              // Show update notification
              if (confirm('🌟 A new version is available! Reload to update?')) {
                newWorker.postMessage({ type: 'SKIP_WAITING' });
                window.location.reload();
              }
            }
          });
        }
      });

      // Listen for controlling service worker change
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('🔄 Service worker controller changed');
        window.location.reload();
      });

      // Periodic update check (every 30 minutes)
      setInterval(() => {
        registration.update();
      }, 30 * 60 * 1000);

    } catch (error) {
      console.error('❌ SW registration failed:', error);
    }
  }
};

// Handle app shortcuts and deep links for PWA
const handlePWAShortcuts = () => {
  // Handle launch queue for file handling
  if ('launchQueue' in window) {
    (window as any).launchQueue.setConsumer((launchParams: any) => {
      if (launchParams.targetURL) {
        const url = new URL(launchParams.targetURL);
        const action = url.searchParams.get('action');
        
        // Dispatch custom events for shortcuts
        if (action === 'new') {
          window.dispatchEvent(new CustomEvent('pwa-shortcut', { detail: { action: 'new' } }));
        } else if (action === 'search') {
          window.dispatchEvent(new CustomEvent('pwa-shortcut', { detail: { action: 'search' } }));
        } else if (action === 'voice') {
          window.dispatchEvent(new CustomEvent('pwa-shortcut', { detail: { action: 'voice' } }));
        } else if (action === 'photo') {
          window.dispatchEvent(new CustomEvent('pwa-shortcut', { detail: { action: 'photo' } }));
        }
      }
    });
  }

  // Handle URL parameters for direct actions
  const urlParams = new URLSearchParams(window.location.search);
  const action = urlParams.get('action');
  if (action) {
    window.dispatchEvent(new CustomEvent('pwa-shortcut', { detail: { action } }));
  }
};

// iOS PWA specific optimizations
const optimizeForIOS = () => {
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  const isStandalone = window.navigator.standalone === true;

  if (isIOS) {
    // Prevent zoom on input focus
    document.addEventListener('touchstart', () => {}, { passive: true });

    // Handle orientation changes
    const handleOrientationChange = () => {
      const viewport = document.querySelector('meta[name=viewport]') as HTMLMetaElement;
      if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no');
      }
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);

    // Prevent pull-to-refresh
    let lastTouchY = 0;
    document.addEventListener('touchstart', (e) => {
      if (e.touches.length === 1) {
        lastTouchY = e.touches[0].clientY;
      }
    }, { passive: false });

    document.addEventListener('touchmove', (e) => {
      const touchY = e.touches[0].clientY;
      const touchYDelta = touchY - lastTouchY;
      lastTouchY = touchY;

      if (e.touches.length === 1 && touchYDelta > 0 && window.pageYOffset === 0) {
        e.preventDefault();
      }
    }, { passive: false });

    // Add iOS-specific CSS class
    document.body.classList.add('ios-device');
    if (isStandalone) {
      document.body.classList.add('ios-standalone');
    }
  }
};

// Initialize PWA features
const initializePWA = () => {
  registerServiceWorker();
  handlePWAShortcuts();
  optimizeForIOS();

  // Performance monitoring
  if ('performance' in window) {
    window.addEventListener('load', () => {
      const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      console.log('📊 Page load performance:', {
        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
        loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
        totalTime: perfData.loadEventEnd - perfData.fetchStart
      });
    });
  }
};

// Start the app
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);

// Initialize PWA features after React is mounted
initializePWA();
