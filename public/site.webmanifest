{"name": "<PERSON><PERSON>y and <PERSON> Stories", "short_name": "W&B Stories", "description": "A private shared note space for <PERSON> and <PERSON><PERSON><PERSON>. Create, edit, and share notes in a cute kawaii environment.", "icons": [{"src": "/favicon.svg", "sizes": "any", "type": "image/svg+xml", "purpose": "any"}, {"src": "/icon-48.png", "sizes": "48x48", "type": "image/png", "purpose": "any"}, {"src": "/icon-72.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "/icon-96.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "/icon-128.png", "sizes": "128x128", "type": "image/png", "purpose": "any"}, {"src": "/icon-144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/icon-152.png", "sizes": "152x152", "type": "image/png", "purpose": "any"}, {"src": "/icon-168.png", "sizes": "168x168", "type": "image/png", "purpose": "any"}, {"src": "/icon-180.png", "sizes": "180x180", "type": "image/png", "purpose": "any"}, {"src": "/icon-192.png", "sizes": "192x192", "type": "image/png", "purpose": "any"}, {"src": "/icon-256.png", "sizes": "256x256", "type": "image/png", "purpose": "any"}, {"src": "/icon-384.png", "sizes": "384x384", "type": "image/png", "purpose": "any"}, {"src": "/icon-512.png", "sizes": "512x512", "type": "image/png", "purpose": "any"}, {"src": "/icon-maskable-192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "/icon-maskable-512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}], "theme_color": "#ec4899", "background_color": "#fdf2f8", "display": "standalone", "display_override": ["window-controls-overlay", "standalone", "minimal-ui"], "start_url": "/", "scope": "/", "orientation": "any", "categories": ["productivity", "utilities", "lifestyle", "social", "entertainment"], "lang": "en", "dir": "ltr", "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "handle_links": "preferred", "launch_handler": {"client_mode": "focus-existing"}, "protocol_handlers": [{"protocol": "web+wickystories", "url": "/?action=open&url=%s"}], "shortcuts": [{"name": "New Story", "short_name": "New", "description": "Create a new magical story", "url": "/?action=new", "icons": [{"src": "/icon-192.png", "sizes": "192x192", "type": "image/png"}]}, {"name": "Search Stories", "short_name": "Search", "description": "Search through your magical stories", "url": "/?action=search", "icons": [{"src": "/icon-192.png", "sizes": "192x192", "type": "image/png"}]}, {"name": "Voice Story", "short_name": "Voice", "description": "Create a story with voice recording", "url": "/?action=voice", "icons": [{"src": "/icon-192.png", "sizes": "192x192", "type": "image/png"}]}, {"name": "Photo Story", "short_name": "Photo", "description": "Create a story with photos", "url": "/?action=photo", "icons": [{"src": "/icon-192.png", "sizes": "192x192", "type": "image/png"}]}]}