<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no" />
    <title>W<PERSON>y and Batman Stories - Magical Story Space</title>
    <meta name="description" content="A private shared note space for Batman and Wicky. Create, edit, and share notes in a cute kawaii environment.">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#ec4899">
    <meta name="msapplication-TileColor" content="#ec4899">
    <meta name="msapplication-config" content="/browserconfig.xml">
    
    <!-- iOS Specific Meta Tags - Critical for PWA -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="W&B Stories">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="format-detection" content="address=no">
    
    <!-- Android Specific Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="W&B Stories">
    
    <!-- Windows Specific Meta Tags -->
    <meta name="msapplication-starturl" content="/">
    <meta name="msapplication-window" content="width=1024;height=768">
    <meta name="msapplication-navbutton-color" content="#ec4899">
    
    <!-- Critical iOS Icons - Must be PNG with white background -->
    <link rel="apple-touch-icon" sizes="57x57" href="/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon-180x180.png">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    
    <!-- Standard Icons - Progressive Enhancement -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="icon" type="image/png" sizes="512x512" href="/icon-512.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/icon-192.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/icon-96.png">
    <link rel="icon" type="image/png" sizes="48x48" href="/icon-48.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/icon-32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icon-16.png">
    
    <!-- Web App Manifest -->
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- iOS Splash Screens - Critical for proper PWA experience -->
    <!-- iPhone SE -->
    <link rel="apple-touch-startup-image" href="/splash-640x1136.png" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <!-- iPhone 8, 7, 6s, 6 -->
    <link rel="apple-touch-startup-image" href="/splash-750x1334.png" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <!-- iPhone 8 Plus, 7 Plus, 6s Plus, 6 Plus -->
    <link rel="apple-touch-startup-image" href="/splash-1242x2208.png" media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone X, XS, 11 Pro -->
    <link rel="apple-touch-startup-image" href="/splash-1125x2436.png" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone XR, 11 -->
    <link rel="apple-touch-startup-image" href="/splash-828x1792.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <!-- iPhone XS Max, 11 Pro Max -->
    <link rel="apple-touch-startup-image" href="/splash-1242x2688.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone 12 mini -->
    <link rel="apple-touch-startup-image" href="/splash-1080x2340.png" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone 12, 12 Pro -->
    <link rel="apple-touch-startup-image" href="/splash-1170x2532.png" media="(device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone 12 Pro Max -->
    <link rel="apple-touch-startup-image" href="/splash-1284x2778.png" media="(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone 13 mini -->
    <link rel="apple-touch-startup-image" href="/splash-1080x2340.png" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone 13, 13 Pro, 14 -->
    <link rel="apple-touch-startup-image" href="/splash-1170x2532.png" media="(device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone 13 Pro Max, 14 Plus -->
    <link rel="apple-touch-startup-image" href="/splash-1284x2778.png" media="(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone 14 Pro -->
    <link rel="apple-touch-startup-image" href="/splash-1179x2556.png" media="(device-width: 393px) and (device-height: 852px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone 14 Pro Max -->
    <link rel="apple-touch-startup-image" href="/splash-1290x2796.png" media="(device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone 15, 15 Pro -->
    <link rel="apple-touch-startup-image" href="/splash-1179x2556.png" media="(device-width: 393px) and (device-height: 852px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <!-- iPhone 15 Plus, 15 Pro Max -->
    <link rel="apple-touch-startup-image" href="/splash-1290x2796.png" media="(device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    
    <!-- iPad Splash Screens -->
    <link rel="apple-touch-startup-image" href="/splash-1536x2048.png" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <link rel="apple-touch-startup-image" href="/splash-1668x2224.png" media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <link rel="apple-touch-startup-image" href="/splash-2048x2732.png" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/src/main.tsx" as="script">
    
    <!-- Security headers -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- Prevent iOS zoom on input focus -->
    <style>
      input, textarea, select {
        font-size: 16px !important;
      }
      
      /* iOS PWA specific styles */
      @supports (-webkit-touch-callout: none) {
        .ios-safe-area {
          padding-top: env(safe-area-inset-top);
          padding-bottom: env(safe-area-inset-bottom);
          padding-left: env(safe-area-inset-left);
          padding-right: env(safe-area-inset-right);
        }
      }
      
      /* Prevent pull-to-refresh on iOS */
      body {
        overscroll-behavior-y: contain;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Enhanced Service Worker Registration for iOS -->
    <script>
      // iOS PWA detection
      const isIOSPWA = window.navigator.standalone === true;
      const isIOSSafari = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
      
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js', {
            scope: '/',
            updateViaCache: 'none'
          })
            .then((registration) => {
              console.log('SW registered: ', registration);
              
              // Force update check on iOS
              if (isIOSSafari || isIOSPWA) {
                registration.update();
              }
              
              // Check for updates
              registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                if (newWorker) {
                  newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                      // New content is available
                      if (confirm('New version available! Reload to update?')) {
                        newWorker.postMessage({ type: 'SKIP_WAITING' });
                        window.location.reload();
                      }
                    }
                  });
                }
              });
              
              // Listen for controlling service worker change
              navigator.serviceWorker.addEventListener('controllerchange', () => {
                window.location.reload();
              });
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
      
      // Handle app shortcuts and deep links
      if ('launchQueue' in window) {
        window.launchQueue.setConsumer((launchParams) => {
          if (launchParams.targetURL) {
            const url = new URL(launchParams.targetURL);
            const action = url.searchParams.get('action');
            
            // Handle shortcuts
            if (action === 'new') {
              window.dispatchEvent(new CustomEvent('pwa-shortcut', { detail: { action: 'new' } }));
            } else if (action === 'search') {
              window.dispatchEvent(new CustomEvent('pwa-shortcut', { detail: { action: 'search' } }));
            }
          }
        });
      }
      
      // iOS specific fixes
      if (isIOSSafari || isIOSPWA) {
        // Prevent zoom on input focus
        document.addEventListener('touchstart', function() {}, { passive: true });
        
        // Handle iOS viewport changes
        const handleViewportChange = () => {
          const viewport = document.querySelector('meta[name=viewport]');
          if (viewport) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no');
          }
        };
        
        window.addEventListener('orientationchange', handleViewportChange);
        window.addEventListener('resize', handleViewportChange);
        
        // Prevent pull-to-refresh
        let startY = 0;
        document.addEventListener('touchstart', (e) => {
          startY = e.touches[0].clientY;
        }, { passive: true });
        
        document.addEventListener('touchmove', (e) => {
          const y = e.touches[0].clientY;
          if (y > startY && window.scrollY === 0) {
            e.preventDefault();
          }
        }, { passive: false });
      }
      
      // Handle PWA installation state
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        window.deferredPrompt = e;
      });
      
      window.addEventListener('appinstalled', () => {
        console.log('PWA was installed');
        window.deferredPrompt = null;
      });
    </script>
  </body>
</html>