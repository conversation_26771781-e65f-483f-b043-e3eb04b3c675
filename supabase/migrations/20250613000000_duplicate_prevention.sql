/*
  # Duplicate Prevention Migration
  
  This migration adds database-level constraints and indexes to prevent duplicate notes
  from being saved, enhancing the application-level duplicate prevention measures.
  
  1. Unique constraints
  2. Indexes for performance
  3. Functions for content similarity checking
*/

-- Create a function to normalize content for comparison
CREATE OR REPLACE FUNCTION normalize_content(content text)
RETURNS text AS $$
BEGIN
  -- Remove extra whitespace, convert to lowercase for comparison
  RETURN trim(regexp_replace(lower(content), '\s+', ' ', 'g'));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- First, clean up existing duplicates before creating the unique index
WITH duplicate_notes AS (
  SELECT 
    id,
    ROW_NUMBER() OVER (
      PARTITION BY 
        author_name, 
        md5(normalize_content(title) || ':' || normalize_content(content))
      ORDER BY created_at ASC
    ) as rn
  FROM community_notes
)
DELETE FROM community_notes 
WHERE id IN (
  SELECT id FROM duplicate_notes WHERE rn > 1
);

-- Create a regular unique index to prevent duplicate content from same author
-- Note: Time-based uniqueness is handled at application level for better performance
CREATE UNIQUE INDEX IF NOT EXISTS idx_community_notes_content_dedup 
ON community_notes (
  author_name, 
  md5(normalize_content(title) || ':' || normalize_content(content))
);

-- Create an index for efficient duplicate checking
CREATE INDEX IF NOT EXISTS idx_community_notes_content_hash 
ON community_notes (
  author_name,
  md5(normalize_content(title) || ':' || normalize_content(content)),
  created_at DESC
);

-- Create a function to check for recent duplicates
CREATE OR REPLACE FUNCTION check_duplicate_note(
  p_title text,
  p_content text,
  p_author_name text
) RETURNS boolean AS $$
DECLARE
  duplicate_count integer;
BEGIN
  SELECT COUNT(*)
  INTO duplicate_count
  FROM community_notes
  WHERE author_name = p_author_name
    AND normalize_content(title) = normalize_content(p_title)
    AND normalize_content(content) = normalize_content(p_content)
    AND created_at >= (now() - interval '1 minute');
    
  RETURN duplicate_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger function to prevent duplicates
CREATE OR REPLACE FUNCTION prevent_duplicate_notes()
RETURNS trigger AS $$
BEGIN
  -- Check for duplicates before insert
  IF TG_OP = 'INSERT' THEN
    IF check_duplicate_note(NEW.title, NEW.content, NEW.author_name) THEN
      RAISE EXCEPTION 'Duplicate note detected: Same content from same author within the last minute'
        USING ERRCODE = 'unique_violation';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger (optional - can be enabled if strict prevention is needed)
-- Commented out by default to avoid breaking existing functionality
-- Uncomment if you want database-level duplicate prevention
/*
CREATE TRIGGER trigger_prevent_duplicate_notes
  BEFORE INSERT ON community_notes
  FOR EACH ROW
  EXECUTE FUNCTION prevent_duplicate_notes();
*/

-- Create additional indexes for performance
CREATE INDEX IF NOT EXISTS idx_community_notes_author_recent 
ON community_notes (author_name, created_at DESC);

-- Add a comment to the table documenting the duplicate prevention measures
COMMENT ON TABLE community_notes IS 'Community notes table with duplicate prevention measures including content normalization and time-based constraints'; 