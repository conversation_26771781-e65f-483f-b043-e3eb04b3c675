/*
  # Add increment likes function

  1. Functions
    - `increment_likes` - Safely increment the likes count for a note
  
  2. Security
    - Function is accessible to public for community interaction
*/

-- Create function to safely increment likes
CREATE OR REPLACE FUNCTION increment_likes(note_id uuid)
R<PERSON><PERSON>NS void AS $$
BEGIN
  UPDATE community_notes 
  SET likes_count = likes_count + 1 
  WHERE id = note_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to public
GRANT EXECUTE ON FUNCTION increment_likes(uuid) TO public;