/*
  # Add Media Support to Community Notes

  1. New Tables
    - `note_attachments` - Store image and voice note metadata
      - `id` (uuid, primary key)
      - `note_id` (uuid, foreign key to community_notes)
      - `type` (text) - 'image' or 'voice'
      - `file_name` (text)
      - `file_size` (integer)
      - `mime_type` (text)
      - `storage_path` (text)
      - `thumbnail_path` (text, for images)
      - `duration_seconds` (integer, for voice notes)
      - `metadata` (jsonb) - Additional data like dimensions, waveform
      - `created_at` (timestamp)

  2. Storage Buckets
    - Create storage buckets for media files
    - Set up RLS policies for secure access

  3. Security
    - Enable RLS on attachments table
    - Add policies for public access (matching community notes)
*/

-- Create note_attachments table
CREATE TABLE IF NOT EXISTS note_attachments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  note_id uuid REFERENCES community_notes(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('image', 'voice')),
  file_name text NOT NULL,
  file_size integer NOT NULL,
  mime_type text NOT NULL,
  storage_path text NOT NULL,
  thumbnail_path text,
  duration_seconds integer,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE note_attachments ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (matching community notes pattern)
CREATE POLICY "Anyone can read note attachments"
  ON note_attachments
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Anyone can create note attachments"
  ON note_attachments
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Anyone can update note attachments"
  ON note_attachments
  FOR UPDATE
  TO public
  USING (true);

CREATE POLICY "Anyone can delete note attachments"
  ON note_attachments
  FOR DELETE
  TO public
  USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_note_attachments_note_id ON note_attachments(note_id);
CREATE INDEX IF NOT EXISTS idx_note_attachments_type ON note_attachments(type);
CREATE INDEX IF NOT EXISTS idx_note_attachments_created_at ON note_attachments(created_at DESC);

-- Insert storage bucket creation commands (these would be run via Supabase dashboard or API)
-- Storage buckets: 'note-images', 'note-voice', 'note-thumbnails'