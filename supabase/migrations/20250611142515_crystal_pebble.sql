/*
  # Simplify notes schema

  1. Changes
    - Remove likes_count column (no longer needed)
    - Remove is_pinned column (no longer needed)
    - Remove increment_likes function (no longer needed)
    - Update indexes to remove likes and pinned references
    - Keep simple ordering by created_at

  2. Security
    - Keep existing RLS policies as they work for personal use
*/

-- Remove the increment_likes function
DROP FUNCTION IF EXISTS increment_likes(uuid);

-- Remove likes_count and is_pinned columns
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'community_notes' AND column_name = 'likes_count'
  ) THEN
    ALTER TABLE community_notes DROP COLUMN likes_count;
  END IF;
  
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'community_notes' AND column_name = 'is_pinned'
  ) THEN
    ALTER TABLE community_notes DROP COLUMN is_pinned;
  END IF;
END $$;

-- Drop old indexes that referenced removed columns
DROP INDEX IF EXISTS idx_community_notes_likes;
DROP INDEX IF EXISTS idx_community_notes_pinned;

-- Keep the created_at index for simple chronological ordering
-- (This index already exists, so no need to recreate)