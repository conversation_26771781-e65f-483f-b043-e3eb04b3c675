/*
  # Create Storage Buckets for Media Uploads

  1. Storage Buckets
    - `note-images` - For storing uploaded images
    - `note-voice` - For storing voice recordings
    - `note-thumbnails` - For storing image thumbnails

  2. Storage Policies
    - Public read access for all buckets
    - Public upload/delete access for authenticated and anonymous users
    - File size and type restrictions

  3. Security
    - Enable RLS on storage objects
    - Allow public access for community app usage
*/

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('note-images', 'note-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
  ('note-voice', 'note-voice', true, 52428800, ARRAY['audio/mp3', 'audio/wav', 'audio/m4a', 'audio/webm', 'audio/ogg', 'audio/mpeg']),
  ('note-thumbnails', 'note-thumbnails', true, 2097152, ARRAY['image/jpeg', 'image/png', 'image/webp'])
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for note-images bucket
CREATE POLICY "Public can view images"
  ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'note-images');

CREATE POLICY "Anyone can upload images"
  ON storage.objects
  FOR INSERT
  TO public
  WITH CHECK (bucket_id = 'note-images');

CREATE POLICY "Anyone can update images"
  ON storage.objects
  FOR UPDATE
  TO public
  USING (bucket_id = 'note-images');

CREATE POLICY "Anyone can delete images"
  ON storage.objects
  FOR DELETE
  TO public
  USING (bucket_id = 'note-images');

-- Create storage policies for note-voice bucket
CREATE POLICY "Public can view voice notes"
  ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'note-voice');

CREATE POLICY "Anyone can upload voice notes"
  ON storage.objects
  FOR INSERT
  TO public
  WITH CHECK (bucket_id = 'note-voice');

CREATE POLICY "Anyone can update voice notes"
  ON storage.objects
  FOR UPDATE
  TO public
  USING (bucket_id = 'note-voice');

CREATE POLICY "Anyone can delete voice notes"
  ON storage.objects
  FOR DELETE
  TO public
  USING (bucket_id = 'note-voice');

-- Create storage policies for note-thumbnails bucket
CREATE POLICY "Public can view thumbnails"
  ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'note-thumbnails');

CREATE POLICY "Anyone can upload thumbnails"
  ON storage.objects
  FOR INSERT
  TO public
  WITH CHECK (bucket_id = 'note-thumbnails');

CREATE POLICY "Anyone can update thumbnails"
  ON storage.objects
  FOR UPDATE
  TO public
  USING (bucket_id = 'note-thumbnails');

CREATE POLICY "Anyone can delete thumbnails"
  ON storage.objects
  FOR DELETE
  TO public
  USING (bucket_id = 'note-thumbnails');

/*
  # Add Video Support to Note Attachments

  1. Changes
    - Update type constraint to include 'video'
    - Add video storage bucket support
    - Update comments to reflect video support

  2. Storage
    - Create 'note_videos' bucket for video files
*/

-- Update the type constraint to include video
ALTER TABLE note_attachments DROP CONSTRAINT IF EXISTS note_attachments_type_check;
ALTER TABLE note_attachments ADD CONSTRAINT note_attachments_type_check 
  CHECK (type IN ('image', 'voice', 'video'));

-- Create index for video type optimization
CREATE INDEX IF NOT EXISTS idx_note_attachments_video_type ON note_attachments(type) WHERE type = 'video';

-- Note: Storage bucket 'note_videos' should be created via Supabase dashboard
-- with the same RLS policies as other media buckets