/*
  # Community Notes Schema

  1. New Tables
    - `community_notes`
      - `id` (uuid, primary key)
      - `title` (text)
      - `content` (text)
      - `author_name` (text) - Anonymous display name
      - `author_color` (text) - Color for visual identification
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
      - `likes_count` (integer) - Number of likes
      - `is_pinned` (boolean) - Community moderator feature

  2. Security
    - Enable RLS on `community_notes` table
    - Add policies for public read access
    - Add policies for authenticated users to create/update their own notes
    - Add policy for public to increment likes

  3. Real-time
    - Enable real-time subscriptions for live updates
*/

-- Create community_notes table
CREATE TABLE IF NOT EXISTS community_notes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL DEFAULT '',
  content text NOT NULL DEFAULT '',
  author_name text NOT NULL DEFAULT 'Anonymous Kitty',
  author_color text NOT NULL DEFAULT '#ec4899',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  likes_count integer DEFAULT 0,
  is_pinned boolean DEFAULT false
);

-- Enable Row Level Security
ALTER TABLE community_notes ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (community app)
CREATE POLICY "Anyone can read community notes"
  ON community_notes
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Anyone can create community notes"
  ON community_notes
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Anyone can update community notes"
  ON community_notes
  FOR UPDATE
  TO public
  USING (true);

CREATE POLICY "Anyone can delete community notes"
  ON community_notes
  FOR DELETE
  TO public
  USING (true);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_community_notes_updated_at
  BEFORE UPDATE ON community_notes
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_community_notes_created_at ON community_notes(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_community_notes_likes ON community_notes(likes_count DESC);
CREATE INDEX IF NOT EXISTS idx_community_notes_pinned ON community_notes(is_pinned DESC, created_at DESC);