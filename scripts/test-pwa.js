import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const publicDir = path.join(__dirname, '../public');
const manifestPath = path.join(publicDir, 'site.webmanifest');

console.log('🔍 Testing PWA Configuration...\n');

// Test 1: Check if manifest exists and is valid JSON
console.log('📋 Testing Web App Manifest...');
try {
  const manifestContent = fs.readFileSync(manifestPath, 'utf8');
  const manifest = JSON.parse(manifestContent);
  
  console.log('✅ Manifest file exists and is valid JSON');
  console.log(`   • Name: ${manifest.name}`);
  console.log(`   • Short Name: ${manifest.short_name}`);
  console.log(`   • Description: ${manifest.description}`);
  console.log(`   • Display Mode: ${manifest.display}`);
  console.log(`   • Theme Color: ${manifest.theme_color}`);
  console.log(`   • Background Color: ${manifest.background_color}`);
  console.log(`   • Icons: ${manifest.icons.length} defined`);
  console.log(`   • Shortcuts: ${manifest.shortcuts.length} defined`);
} catch (error) {
  console.error('❌ Manifest file error:', error.message);
}

// Test 2: Check if all required icons exist
console.log('\n🎨 Testing Icon Files...');

const requiredIcons = [
  // Core PWA icons
  'icon-192.png',
  'icon-512.png',
  
  // Maskable icons
  'icon-maskable-192.png',
  'icon-maskable-512.png',
  
  // Apple touch icons
  'apple-touch-icon.png',
  'apple-touch-icon-180x180.png',
  
  // Standard favicon sizes
  'icon-16.png',
  'icon-32.png',
  'icon-48.png',
  'icon-96.png',
  
  // SVG favicon
  'favicon.svg'
];

let missingIcons = 0;
let existingIcons = 0;

for (const icon of requiredIcons) {
  const iconPath = path.join(publicDir, icon);
  if (fs.existsSync(iconPath)) {
    const stats = fs.statSync(iconPath);
    console.log(`✅ ${icon} (${Math.round(stats.size / 1024)}KB)`);
    existingIcons++;
  } else {
    console.log(`❌ Missing: ${icon}`);
    missingIcons++;
  }
}

// Test 3: Check all generated icons
console.log('\n📁 Checking Generated Icons...');

const iconSizes = [16, 20, 24, 30, 32, 36, 40, 48, 57, 60, 64, 70, 72, 76, 80, 96, 114, 120, 128, 144, 150, 152, 168, 180, 192, 256, 384, 512];
let generatedIcons = 0;

for (const size of iconSizes) {
  const iconPath = path.join(publicDir, `icon-${size}.png`);
  if (fs.existsSync(iconPath)) {
    generatedIcons++;
  }
}

console.log(`✅ Generated ${generatedIcons}/${iconSizes.length} standard icons`);

// Test 4: Check Apple touch icons
console.log('\n🍎 Checking Apple Touch Icons...');

const appleSizes = [57, 60, 72, 76, 114, 120, 144, 152, 180];
let appleIcons = 0;

for (const size of appleSizes) {
  const iconPath = path.join(publicDir, `apple-touch-icon-${size}x${size}.png`);
  if (fs.existsSync(iconPath)) {
    appleIcons++;
  }
}

console.log(`✅ Generated ${appleIcons}/${appleSizes.length} Apple touch icons`);

// Test 5: Check splash screens
console.log('\n📱 Checking iOS Splash Screens...');

const splashScreens = [
  'splash-640x1136.png',
  'splash-750x1334.png',
  'splash-1242x2208.png',
  'splash-1125x2436.png',
  'splash-828x1792.png',
  'splash-1242x2688.png',
  'splash-1080x2340.png',
  'splash-1170x2532.png',
  'splash-1284x2778.png',
  'splash-1179x2556.png',
  'splash-1290x2796.png',
  'splash-1536x2048.png',
  'splash-1668x2224.png',
  'splash-2048x2732.png'
];

let splashCount = 0;

for (const splash of splashScreens) {
  const splashPath = path.join(publicDir, splash);
  if (fs.existsSync(splashPath)) {
    splashCount++;
  }
}

console.log(`✅ Generated ${splashCount}/${splashScreens.length} iOS splash screens`);

// Test 6: Check index.html for proper icon references
console.log('\n📄 Testing HTML Icon References...');

const indexPath = path.join(__dirname, '../index.html');
try {
  const htmlContent = fs.readFileSync(indexPath, 'utf8');
  
  const checks = [
    { name: 'Favicon SVG', pattern: /favicon\.svg/ },
    { name: 'Apple Touch Icon', pattern: /apple-touch-icon/ },
    { name: 'Manifest Link', pattern: /site\.webmanifest/ },
    { name: 'Theme Color Meta', pattern: /theme-color/ },
    { name: 'Apple Mobile Web App', pattern: /apple-mobile-web-app-capable/ },
    { name: 'Viewport Meta', pattern: /viewport.*viewport-fit=cover/ }
  ];
  
  for (const check of checks) {
    if (check.pattern.test(htmlContent)) {
      console.log(`✅ ${check.name} properly referenced`);
    } else {
      console.log(`❌ Missing: ${check.name}`);
    }
  }
} catch (error) {
  console.error('❌ Error reading index.html:', error.message);
}

// Test 7: PWA Manifest Validation
console.log('\n🔧 PWA Manifest Validation...');

try {
  const manifestContent = fs.readFileSync(manifestPath, 'utf8');
  const manifest = JSON.parse(manifestContent);
  
  const validationChecks = [
    { name: 'Has name', check: !!manifest.name },
    { name: 'Has short_name', check: !!manifest.short_name },
    { name: 'Has start_url', check: !!manifest.start_url },
    { name: 'Has display mode', check: !!manifest.display },
    { name: 'Has theme_color', check: !!manifest.theme_color },
    { name: 'Has background_color', check: !!manifest.background_color },
    { name: 'Has icons array', check: Array.isArray(manifest.icons) },
    { name: 'Has 192px icon', check: manifest.icons.some(icon => icon.sizes.includes('192x192')) },
    { name: 'Has 512px icon', check: manifest.icons.some(icon => icon.sizes.includes('512x512')) },
    { name: 'Has maskable icons', check: manifest.icons.some(icon => icon.purpose === 'maskable') },
    { name: 'Has shortcuts', check: Array.isArray(manifest.shortcuts) && manifest.shortcuts.length > 0 }
  ];
  
  for (const validation of validationChecks) {
    if (validation.check) {
      console.log(`✅ ${validation.name}`);
    } else {
      console.log(`❌ Missing: ${validation.name}`);
    }
  }
} catch (error) {
  console.error('❌ Manifest validation error:', error.message);
}

// Summary
console.log('\n📊 PWA Test Summary:');
console.log(`✅ Required icons: ${existingIcons}/${requiredIcons.length}`);
console.log(`✅ Generated icons: ${generatedIcons}/${iconSizes.length}`);
console.log(`✅ Apple touch icons: ${appleIcons}/${appleSizes.length}`);
console.log(`✅ Splash screens: ${splashCount}/${splashScreens.length}`);

const totalFiles = existingIcons + generatedIcons + appleIcons + splashCount;
console.log(`\n🎉 Total PWA assets: ${totalFiles} files`);

if (missingIcons === 0) {
  console.log('\n🌟 PWA Configuration Complete! Your app is ready for installation on all platforms.');
  console.log('\n📱 Platform Support:');
  console.log('   • ✅ iOS (iPhone/iPad) - Full support with splash screens');
  console.log('   • ✅ Android - Full support with maskable icons');
  console.log('   • ✅ Windows - Full support with multiple sizes');
  console.log('   • ✅ macOS - Full support with high-resolution icons');
  console.log('   • ✅ Chrome/Edge/Firefox - Full PWA support');
} else {
  console.log(`\n⚠️  ${missingIcons} required icons are missing. Run 'npm run generate-icons' to fix this.`);
}

console.log('\n🔗 Next Steps:');
console.log('   1. Test installation on different devices');
console.log('   2. Verify icons appear correctly on home screens');
console.log('   3. Check PWA functionality with Lighthouse');
console.log('   4. Test offline capabilities');
console.log('\n💡 Tip: Use Chrome DevTools > Application > Manifest to test your PWA configuration.'); 