import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Icon sizes required for comprehensive PWA support
const iconSizes = {
  // Core PWA sizes (required by Lighthouse)
  core: [192, 512],
  
  // iOS specific sizes
  ios: [57, 60, 72, 76, 114, 120, 144, 152, 180],
  
  // Android/Chrome sizes
  android: [48, 72, 96, 144, 168, 192, 256, 384, 512],
  
  // Windows PWA sizes
  windows: [16, 20, 24, 30, 32, 36, 40, 48, 60, 64, 70, 80, 96, 150, 256],
  
  // Standard web favicon sizes
  web: [16, 32, 48, 96, 128, 256],
  
  // Maskable icon sizes (with safe zone)
  maskable: [192, 512]
};

// Get all unique sizes
const allSizes = [...new Set([
  ...iconSizes.core,
  ...iconSizes.ios,
  ...iconSizes.android,
  ...iconSizes.windows,
  ...iconSizes.web,
  ...iconSizes.maskable
])].sort((a, b) => a - b);

console.log('🎨 Generating icons from favicon.svg...');
console.log(`📏 Creating ${allSizes.length} different sizes: ${allSizes.join(', ')}px`);

const svgPath = path.join(__dirname, '../public/favicon.svg');
const outputDir = path.join(__dirname, '../public');

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Read the SVG file
const svgBuffer = fs.readFileSync(svgPath);

async function generateIcon(size, isMaskable = false) {
  try {
    let outputBuffer;
    
    if (isMaskable) {
      // For maskable icons, we need to add padding for the safe zone
      // Safe zone is 80% of the icon, so we need 10% padding on each side
      const paddedSize = Math.round(size * 1.25); // 25% larger to accommodate padding
      const padding = Math.round((paddedSize - size) / 2);
      
      // Create a canvas with padding and centered icon
      outputBuffer = await sharp(svgBuffer)
        .resize(size, size)
        .extend({
          top: padding,
          bottom: padding,
          left: padding,
          right: padding,
          background: { r: 255, g: 182, b: 193, alpha: 1 } // Light pink background
        })
        .resize(size, size) // Resize back to target size
        .png({
          quality: 100,
          compressionLevel: 6,
          palette: true
        })
        .toBuffer();
      
      const filename = `icon-maskable-${size}.png`;
      fs.writeFileSync(path.join(outputDir, filename), outputBuffer);
      console.log(`✅ Generated maskable icon: ${filename}`);
    } else {
      // Regular icon generation
      outputBuffer = await sharp(svgBuffer)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 0, g: 0, b: 0, alpha: 0 } // Transparent background
        })
        .png({
          quality: 100,
          compressionLevel: 6,
          palette: true
        })
        .toBuffer();
      
      const filename = `icon-${size}.png`;
      fs.writeFileSync(path.join(outputDir, filename), outputBuffer);
      console.log(`✅ Generated icon: ${filename}`);
    }
  } catch (error) {
    console.error(`❌ Error generating ${size}px icon:`, error.message);
  }
}

async function generateAllIcons() {
  console.log('\n🔄 Starting icon generation...\n');
  
  // Generate regular icons
  for (const size of allSizes) {
    await generateIcon(size, false);
  }
  
  console.log('\n🎭 Generating maskable icons...\n');
  
  // Generate maskable icons
  for (const size of iconSizes.maskable) {
    await generateIcon(size, true);
  }
  
  console.log('\n🎯 Generating Apple touch icons...\n');
  
  // Generate Apple touch icons (special naming)
  const appleSizes = [57, 60, 72, 76, 114, 120, 144, 152, 180];
  for (const size of appleSizes) {
    try {
      const outputBuffer = await sharp(svgBuffer)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 1 } // White background for Apple
        })
        .png({
          quality: 100,
          compressionLevel: 6
        })
        .toBuffer();
      
      const filename = `apple-touch-icon-${size}x${size}.png`;
      fs.writeFileSync(path.join(outputDir, filename), outputBuffer);
      console.log(`🍎 Generated Apple touch icon: ${filename}`);
    } catch (error) {
      console.error(`❌ Error generating Apple touch icon ${size}px:`, error.message);
    }
  }
  
  // Generate default Apple touch icon (180x180)
  try {
    const outputBuffer = await sharp(svgBuffer)
      .resize(180, 180, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .png({
        quality: 100,
        compressionLevel: 6
      })
      .toBuffer();
    
    fs.writeFileSync(path.join(outputDir, 'apple-touch-icon.png'), outputBuffer);
    console.log('🍎 Generated default Apple touch icon: apple-touch-icon.png');
  } catch (error) {
    console.error('❌ Error generating default Apple touch icon:', error.message);
  }
  
  console.log('\n🌟 Generating splash screens for iOS...\n');
  
  // Generate iOS splash screens
  const splashSizes = [
    { width: 640, height: 1136, name: 'splash-640x1136.png' }, // iPhone SE
    { width: 750, height: 1334, name: 'splash-750x1334.png' }, // iPhone 8, 7, 6s, 6
    { width: 1242, height: 2208, name: 'splash-1242x2208.png' }, // iPhone 8 Plus, 7 Plus, 6s Plus, 6 Plus
    { width: 1125, height: 2436, name: 'splash-1125x2436.png' }, // iPhone X, XS, 11 Pro
    { width: 828, height: 1792, name: 'splash-828x1792.png' }, // iPhone XR, 11
    { width: 1242, height: 2688, name: 'splash-1242x2688.png' }, // iPhone XS Max, 11 Pro Max
    { width: 1080, height: 2340, name: 'splash-1080x2340.png' }, // iPhone 12 mini
    { width: 1170, height: 2532, name: 'splash-1170x2532.png' }, // iPhone 12, 12 Pro
    { width: 1284, height: 2778, name: 'splash-1284x2778.png' }, // iPhone 12 Pro Max
    { width: 1179, height: 2556, name: 'splash-1179x2556.png' }, // iPhone 14 Pro
    { width: 1290, height: 2796, name: 'splash-1290x2796.png' }, // iPhone 14 Pro Max
    { width: 1536, height: 2048, name: 'splash-1536x2048.png' }, // iPad
    { width: 1668, height: 2224, name: 'splash-1668x2224.png' }, // iPad Pro 10.5"
    { width: 2048, height: 2732, name: 'splash-2048x2732.png' }, // iPad Pro 12.9"
  ];
  
  for (const splash of splashSizes) {
    try {
      // Create a centered splash screen with gradient background
      const iconSize = Math.min(splash.width, splash.height) * 0.3; // 30% of screen size
      
      const outputBuffer = await sharp({
        create: {
          width: splash.width,
          height: splash.height,
          channels: 4,
          background: { r: 253, g: 242, b: 248, alpha: 1 } // Light pink background
        }
      })
      .composite([
        {
          input: await sharp(svgBuffer)
            .resize(Math.round(iconSize), Math.round(iconSize))
            .png()
            .toBuffer(),
          top: Math.round((splash.height - iconSize) / 2),
          left: Math.round((splash.width - iconSize) / 2)
        }
      ])
      .png({
        quality: 90,
        compressionLevel: 6
      })
      .toBuffer();
      
      fs.writeFileSync(path.join(outputDir, splash.name), outputBuffer);
      console.log(`📱 Generated splash screen: ${splash.name}`);
    } catch (error) {
      console.error(`❌ Error generating splash screen ${splash.name}:`, error.message);
    }
  }
  
  console.log('\n🎉 Icon generation complete!');
  console.log(`📁 All icons saved to: ${outputDir}`);
  console.log('\n📋 Generated files:');
  console.log(`   • ${allSizes.length} regular PNG icons (icon-{size}.png)`);
  console.log(`   • ${iconSizes.maskable.length} maskable PNG icons (icon-maskable-{size}.png)`);
  console.log(`   • ${appleSizes.length + 1} Apple touch icons`);
  console.log(`   • ${splashSizes.length} iOS splash screens`);
  console.log(`   • Total: ${allSizes.length + iconSizes.maskable.length + appleSizes.length + 1 + splashSizes.length} files`);
}

// Run the icon generation
generateAllIcons().catch(console.error); 